import React from 'react'

const Car = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
            <path d="M58.0627 33.3042C58.0619 33.3034 58.061 33.3027 58.06 33.3017C56.1629 30.5875 53.0157 28.8085 49.4603 28.8085H44.6953C44.5401 28.517 44.36 28.1536 44.1644 27.7593C43.154 25.7218 41.463 22.3114 38.559 19.3259C35.0212 15.6888 30.5934 13.6021 25.3985 13.1236C17.5304 12.3991 11.3165 14.2197 6.93006 18.5357C3.69088 21.7227 1.47357 26.3435 0.517675 31.8986C-0.159434 35.8336 0.0114251 38.9091 0.0400188 39.3372C0.0547845 42.9308 1.94443 46.3034 4.99213 48.2015C4.98639 48.3336 4.98205 48.4661 4.98205 48.5995C4.98205 53.6306 9.07518 57.7238 14.1064 57.7238C18.7371 57.7238 22.5704 54.2554 23.1517 49.7806H36.467C37.0484 54.2554 40.8817 57.7238 45.5124 57.7238C50.5436 57.7238 54.6367 53.6306 54.6367 48.5995C54.6367 48.5383 54.6333 48.478 54.6322 48.4171C57.8042 46.6133 59.8422 43.2433 59.9413 39.5748C59.982 39.4803 60.0029 39.3775 59.9996 39.2728C59.873 35.1698 58.1367 33.3785 58.0627 33.3042ZM56.0219 34.5126C56.8719 35.6757 57.4198 37.0719 57.5513 38.5859H53.387C52.264 38.5859 51.3504 37.6723 51.3504 36.5492C51.3504 35.4262 52.264 34.5125 53.387 34.5125L56.0219 34.5126ZM14.1064 55.3617C10.3777 55.3617 7.34432 52.3282 7.34432 48.5996C7.34432 44.8709 10.3778 41.8375 14.1064 41.8375C17.8351 41.8375 20.8685 44.8711 20.8685 48.5996C20.8685 52.3282 17.8349 55.3617 14.1064 55.3617ZM45.5124 55.3617C41.7837 55.3617 38.7503 52.3282 38.7503 48.5996C38.7503 44.8709 41.7838 41.8375 45.5124 41.8375C49.2411 41.8375 52.2745 44.8711 52.2745 48.5996C52.2745 52.3282 49.2411 55.3617 45.5124 55.3617ZM54.2212 45.8758C53.06 42.1709 49.5952 39.4753 45.5124 39.4753C40.8817 39.4753 37.0484 42.9437 36.467 47.4185H23.1517C22.5704 42.9437 18.7371 39.4753 14.1064 39.4753C10.1058 39.4753 6.70084 42.0644 5.47271 45.6542C3.55646 44.1244 2.40217 41.7757 2.40217 39.2947C2.40217 39.2655 2.401 39.2355 2.39889 39.2064C2.38998 39.0861 1.58795 27.0946 8.59611 20.2106C12.4637 16.4114 18.0441 14.8187 25.182 15.476C35.9276 16.4656 40.0617 24.8025 42.0483 28.8089C42.4437 29.6062 42.756 30.236 43.0483 30.6601C43.2687 30.9799 43.6324 31.1709 44.0208 31.1709H49.4606C51.4571 31.1709 53.2865 31.8962 54.7026 33.0953H53.387C51.4825 33.0953 49.9331 34.6448 49.9331 36.5493C49.9331 38.4539 51.4825 40.0033 53.387 40.0033H57.5517C57.3437 42.3529 56.1125 44.5011 54.2212 45.8758Z" fill="currentColor" />
            <path d="M6.31962 29.1945C5.68845 29.0305 5.04345 29.4089 4.87915 30.0402C4.76044 30.4653 4.70712 30.7128 4.62309 31.1497C4.49395 31.7891 4.90763 32.4121 5.547 32.5413C5.62598 32.5572 5.7045 32.5648 5.78208 32.5648C6.33251 32.5648 6.8254 32.1778 6.9386 31.6174C7.01524 31.2196 7.05438 31.0348 7.16524 30.635C7.32942 30.0037 6.95079 29.3588 6.31962 29.1945Z" fill="currentColor" />
            <path d="M5.33712 33.7204C4.6879 33.6578 4.11064 34.1331 4.04794 34.7824C4.00259 35.2308 3.97962 35.4747 3.9481 36.016C3.90673 36.6669 4.40091 37.2283 5.05189 37.2696C5.07743 37.2713 5.10275 37.2721 5.12794 37.2721C5.74599 37.2721 6.26583 36.7913 6.30556 36.1658C6.33556 35.6475 6.35712 35.4272 6.39919 35.0096C6.462 34.3603 5.98646 33.7831 5.33712 33.7204Z" fill="currentColor" />
            <path d="M14.1063 45.543C12.4201 45.543 11.0483 46.9148 11.0483 48.601C11.0483 50.2872 12.4201 51.659 14.1063 51.659C15.7926 51.659 17.1644 50.2872 17.1644 48.601C17.1642 46.9148 15.7926 45.543 14.1063 45.543ZM14.1063 50.2416C13.2017 50.2416 12.4656 49.5055 12.4656 48.6009C12.4656 47.6962 13.2017 46.9602 14.1063 46.9602C15.0109 46.9602 15.747 47.6962 15.747 48.6009C15.747 49.5057 15.0109 50.2416 14.1063 50.2416Z" fill="currentColor" />
            <path d="M45.5124 45.543C43.8261 45.543 42.4543 46.9148 42.4543 48.601C42.4543 50.2872 43.8261 51.659 45.5124 51.659C47.1986 51.659 48.5704 50.2872 48.5704 48.601C48.5704 46.9148 47.1986 45.543 45.5124 45.543ZM45.5124 50.2416C44.6077 50.2416 43.8717 49.5055 43.8717 48.6009C43.8717 47.6962 44.6077 46.9602 45.5124 46.9602C46.417 46.9602 47.1531 47.6962 47.1531 48.6009C47.1531 49.5057 46.417 50.2416 45.5124 50.2416Z" fill="currentColor" />
            <path d="M25.9297 19.2591C25.5237 19.1906 25.1069 19.1368 24.6905 19.0994C24.016 19.0389 23.3405 19.2676 22.838 19.7268C22.33 20.1914 22.0386 20.8524 22.0386 21.5406V28.7731C22.0386 30.1236 23.1373 31.2223 24.4878 31.2223H33.9352C34.7317 31.2223 35.4563 30.852 35.9232 30.2063C36.3877 29.5639 36.5119 28.7647 36.264 28.0135C35.3493 25.2418 32.7904 20.4187 25.9297 19.2591ZM34.7747 29.3759C34.5775 29.6486 34.2715 29.8052 33.9353 29.8052H24.4879C23.919 29.8052 23.456 29.3423 23.456 28.7732V21.5408C23.456 21.2494 23.5794 20.9696 23.7944 20.773C24.0066 20.5783 24.2827 20.4853 24.5637 20.511C24.9437 20.5451 25.3237 20.5941 25.6934 20.6566C31.8215 21.6924 34.1034 25.9887 34.9181 28.4576C35.0218 28.7718 34.9695 29.1065 34.7747 29.3759Z" fill="currentColor" />
            <path d="M17.2369 21.195C16.4879 20.7519 15.5869 20.7367 14.827 21.1544C11.9474 22.7369 9.78405 25.0294 8.57057 27.7843C8.23565 28.5447 8.30667 29.4142 8.76077 30.1103C9.21475 30.8064 9.98175 31.2219 10.8126 31.2219H15.9943C17.3449 31.2219 18.4435 30.1231 18.4435 28.7726V23.3081C18.4434 22.4319 17.9923 21.6419 17.2369 21.195ZM17.0261 28.7726C17.0261 29.3416 16.5632 29.8046 15.9942 29.8046H10.8125C10.4572 29.8046 10.1421 29.6339 9.94776 29.3361C9.75358 29.0383 9.72429 28.6809 9.86761 28.3557C10.9545 25.8883 12.9055 23.8277 15.5096 22.3965C15.8308 22.22 16.1974 22.2268 16.5154 22.4149C16.8352 22.604 17.0261 22.938 17.0261 23.3081V28.7726Z" fill="currentColor" />
            <path d="M33.7505 38.2266C33.3591 38.2266 33.0419 38.5439 33.0419 38.9352V40.3643C33.0419 40.7557 33.3592 41.0729 33.7505 41.0729C34.1418 41.0729 34.4591 40.7556 34.4591 40.3643V38.9352C34.4593 38.5439 34.1419 38.2266 33.7505 38.2266Z" fill="currentColor" />
            <path d="M37.9419 35.125C37.5505 35.125 37.2333 35.4423 37.2333 35.8336V37.2627C37.2333 37.6541 37.5506 37.9714 37.9419 37.9714C38.3332 37.9714 38.6505 37.654 38.6505 37.2627V35.8336C38.6507 35.4423 38.3333 35.125 37.9419 35.125Z" fill="currentColor" />
            <path d="M50.6977 18.5377L53.204 19.6669C53.3114 19.7152 53.3963 19.8109 53.4393 19.9317L54.4426 22.7527C54.5844 23.1512 55.0856 23.1512 55.2273 22.7527L56.2305 19.9317C56.2734 19.8109 56.3585 19.7153 56.4658 19.6669L58.9721 18.5377C59.3261 18.3782 59.3261 17.814 58.9721 17.6544L56.4658 16.5253C56.3585 16.4769 56.2735 16.3813 56.2305 16.2605L55.2273 13.4395C55.0856 13.041 54.5843 13.041 54.4426 13.4395L53.4393 16.2605C53.3964 16.3813 53.3114 16.4769 53.204 16.5253L50.6977 17.6544C50.3436 17.814 50.3436 18.3782 50.6977 18.5377Z" fill="currentColor" />
            <path d="M52.7694 4.8905C52.917 3.68914 52.0627 2.5956 50.8614 2.448C49.66 2.3004 48.5665 3.15464 48.4189 4.356C48.2713 5.55736 49.1255 6.6509 50.3269 6.7985C51.5282 6.9461 52.6218 6.09186 52.7694 4.8905Z" fill="currentColor" />
            <path d="M28.3394 4.12739C28.8513 4.10177 29.2454 3.66609 29.2198 3.15428C29.1942 2.64246 28.7585 2.24831 28.2467 2.27393C27.7349 2.29955 27.3407 2.73522 27.3663 3.24704C27.3919 3.75886 27.8276 4.153 28.3394 4.12739Z" fill="currentColor" />
            <path d="M32.2165 6.97214L34.0756 7.80968C34.1551 7.84554 34.2182 7.91655 34.25 8.0062L34.9942 10.0987C35.0993 10.3944 35.4711 10.3944 35.5763 10.0987L36.3204 8.0062C36.3523 7.91655 36.4153 7.84565 36.4949 7.80968L38.354 6.97214C38.6166 6.85378 38.6166 6.4353 38.354 6.31694L36.4949 5.4794C36.4153 5.44354 36.3523 5.37253 36.3204 5.283L35.5763 3.1905C35.4711 2.89483 35.0993 2.89483 34.9942 3.1905L34.25 5.283C34.2182 5.37253 34.1551 5.44354 34.0756 5.4794L32.2165 6.31694C31.9539 6.43542 31.9539 6.8539 32.2165 6.97214Z" fill="currentColor" />
        </svg>
    )
}

export default Car