import React from 'react'

const Feeder = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
            <g clipPath="url(#clip0_5563_582)">
                <path d="M58.9757 36.6999C58.9246 33.2813 57.5293 30.2361 55.0469 28.125C52.5646 26.0143 49.3347 25.1264 45.9522 25.625C45.921 25.6295 45.8899 25.6354 45.8587 25.6403V21.123C45.8587 18.3743 43.6225 16.1383 40.8739 16.1383H40.0477V13.4792C40.0477 11.2829 38.2608 9.49609 36.0645 9.49609H33.0732C30.8768 9.49609 29.0902 11.2829 29.0902 13.4792V16.1383H19.1261C16.3775 16.1383 14.1413 18.3745 14.1413 21.123V25.6403C14.1101 25.6355 14.079 25.6296 14.0478 25.625C10.6649 25.1261 7.43548 26.0143 4.95321 28.125C2.47071 30.2361 1.07548 33.2814 1.02438 36.6999C0.975869 39.9393 2.15864 43.1454 4.35462 45.7279C6.55071 48.3104 9.52517 49.9927 12.7302 50.4653C12.9161 50.4927 13.1023 50.5145 13.2885 50.5336C12.9136 51.2489 12.7004 52.0616 12.7004 52.9237V54.833C12.7004 57.6822 15.0183 60.0002 17.8675 60.0002H42.1324C44.9815 60.0002 47.2995 57.6822 47.2995 54.833V52.9237C47.2995 52.0616 47.0862 51.2489 46.7113 50.5336C46.8975 50.5145 47.0839 50.4927 47.2697 50.4653C50.4747 49.9927 53.4493 48.3102 55.6454 45.7279C57.8415 43.1454 59.0241 39.9393 58.9757 36.6999ZM14.1414 43.8378C13.9999 43.8276 13.8576 43.8133 13.7144 43.7922C12.1283 43.5583 10.6292 42.6939 9.49329 41.3582C8.35739 40.0224 7.74497 38.4039 7.76911 36.8009C7.79079 35.3536 8.34263 34.0975 9.32302 33.2636C10.1127 32.5921 11.1238 32.2372 12.2471 32.2372C12.8841 32.237 13.5315 32.3529 14.1414 32.5591V43.8378ZM16.499 24.9687H43.5011V27.0637V27.0641V47.9419C43.0649 47.8219 42.6063 47.7565 42.1325 47.7565H17.8676C17.3938 47.7565 16.9352 47.8219 16.499 47.9419V24.9687ZM45.8587 32.5589C46.4686 32.3528 47.116 32.2369 47.753 32.237C48.8763 32.237 49.8872 32.592 50.677 33.2635C51.6575 34.0973 52.2094 35.3534 52.231 36.8008C52.2549 38.4038 51.6426 40.0223 50.5068 41.3581C49.3708 42.6938 47.8717 43.5583 46.2858 43.7921C46.1425 43.8132 46.0002 43.8276 45.8586 43.8377V32.5589H45.8587ZM31.4478 13.4792C31.4478 12.5829 32.1769 11.8537 33.0732 11.8537H36.0645C36.9608 11.8537 37.69 12.5829 37.69 13.4792V16.1383H31.4477V13.4792H31.4478ZM19.1261 18.4959H40.8739C42.3225 18.4959 43.5011 19.6744 43.5011 21.123V22.611H16.499V21.123C16.499 19.6744 17.6775 18.4959 19.1261 18.4959ZM6.15075 44.2006C4.3252 42.0539 3.34188 39.4027 3.38185 36.7352C3.42263 34.0056 4.52313 31.5857 6.48063 29.9211C8.43802 28.2566 11.0029 27.5593 13.7041 27.9573C13.8499 27.9788 13.9956 28.0047 14.1414 28.0326V31.0809C13.5272 30.9139 12.8775 30.822 12.2471 30.8226C10.7836 30.8226 9.45567 31.294 8.40673 32.1861C7.11122 33.2876 6.38255 34.9191 6.35466 36.7797C6.32548 38.7258 7.05743 40.6773 8.41575 42.2745C9.77396 43.8718 11.5825 44.9077 13.5083 45.1916C13.7204 45.2229 13.9317 45.2433 14.1415 45.2553V48.1207C14.1415 48.1588 14.1436 48.1964 14.1473 48.2337C13.7917 48.2191 13.4324 48.1857 13.0744 48.1329C10.435 47.7438 7.97618 46.3472 6.15075 44.2006ZM44.942 54.8331C44.942 56.3823 43.6818 57.6427 42.1325 57.6427H17.8676C16.3184 57.6427 15.0581 56.3823 15.0581 54.8331V52.9238C15.0581 51.3746 16.3184 50.1142 17.8676 50.1142H42.1325C43.6817 50.1142 44.942 51.3746 44.942 52.9238V54.8331ZM53.8493 44.2006C52.0238 46.3473 49.565 47.7439 46.9258 48.1329C46.5678 48.1858 46.2085 48.2191 45.8529 48.2337C45.8565 48.1966 45.8587 48.1588 45.8587 48.1207V45.2553C46.0687 45.2433 46.2799 45.2228 46.4921 45.1916C48.4177 44.9077 50.2263 43.8718 51.5843 42.2745C52.9427 40.6773 53.6746 38.7258 53.6454 36.7797C53.6175 34.919 52.8889 33.2876 51.5934 32.1861C50.5445 31.294 49.2166 30.8226 47.753 30.8226C47.1223 30.822 46.473 30.914 45.8587 31.0809V28.0327C46.0045 28.0048 46.1502 27.9789 46.296 27.9573C48.9978 27.5593 51.5623 28.2566 53.5195 29.9211C55.4771 31.5856 56.5776 34.0056 56.6183 36.7352C56.6582 39.4027 55.6749 42.0539 53.8493 44.2006Z" fill="currentColor" />
                <path d="M30.0001 44.1885C33.3496 44.1885 36.0748 41.4634 36.0748 38.1137C36.0748 34.974 31.0995 29.2016 30.5322 28.5538C30.3979 28.4004 30.2038 28.3125 30.0001 28.3125C29.7962 28.3125 29.6023 28.4005 29.4679 28.5538C28.9006 29.2017 23.9253 34.9741 23.9253 38.1137C23.9253 41.4634 26.6504 44.1885 30.0001 44.1885ZM30.0002 30.1116C31.6676 32.1206 34.6603 36.1358 34.6603 38.1138C34.6603 40.6835 32.5697 42.774 30.0001 42.774C27.4305 42.774 25.3399 40.6835 25.3399 38.1138C25.3399 36.1376 28.3327 32.1213 30.0002 30.1116Z" fill="currentColor" />
                <path d="M19.7071 31.8594C19.0561 31.8673 18.5347 32.4014 18.5426 33.0524C18.5563 34.189 18.5563 35.4818 18.5426 36.6928C18.5352 37.3438 19.057 37.8775 19.708 37.885H19.7217C20.3665 37.885 20.8928 37.3659 20.9001 36.7195C20.9141 35.4908 20.9141 34.1783 20.9 33.024C20.8922 32.373 20.3566 31.8543 19.7071 31.8594Z" fill="currentColor" />
                <path d="M19.7213 30.1906C20.3724 30.1906 20.9001 29.6629 20.9001 29.0118V28.2999C20.9001 27.6488 20.3724 27.1211 19.7213 27.1211C19.0703 27.1211 18.5425 27.6488 18.5425 28.2999V29.0118C18.5425 29.6628 19.0703 30.1906 19.7213 30.1906Z" fill="currentColor" />
                <path d="M38.869 45.9814C39.2596 45.9814 39.5762 45.6647 39.5762 45.2741V43.8479C39.5762 43.4573 39.2596 43.1406 38.869 43.1406C38.4784 43.1406 38.1616 43.4573 38.1616 43.8479V45.2741C38.1616 45.6647 38.4784 45.9814 38.869 45.9814Z" fill="currentColor" />
                <path d="M35.3675 29.7236C35.7581 29.7236 36.0747 29.4069 36.0747 29.0163V27.59C36.0747 27.1995 35.7581 26.8828 35.3675 26.8828C34.9769 26.8828 34.6603 27.1995 34.6603 27.59V29.0163C34.6603 29.4069 34.9769 29.7236 35.3675 29.7236Z" fill="currentColor" />
                <path d="M39.1994 33.9659C39.59 33.9659 39.9066 33.6492 39.9066 33.2586V31.8322C39.9066 31.4416 39.59 31.125 39.1994 31.125C38.8088 31.125 38.4922 31.4416 38.4922 31.8322V33.2585C38.4922 33.6492 38.8088 33.9659 39.1994 33.9659Z" fill="currentColor" />
                <path d="M8.81821 5.38647L11.3196 6.51346C11.4267 6.56174 11.5115 6.65725 11.5544 6.77771L12.5557 9.59303C12.6971 9.99076 13.1974 9.99076 13.3388 9.59303L14.3401 6.77771C14.383 6.65725 14.4678 6.56162 14.5749 6.51346L17.0763 5.38647C17.4297 5.22732 17.4297 4.66424 17.0763 4.50498L14.5749 3.37799C14.4678 3.32971 14.383 3.2342 14.3401 3.11373L13.3388 0.298301C13.1974 -0.0994336 12.6971 -0.0994336 12.5557 0.298301L11.5544 3.11373C11.5115 3.2342 11.4267 3.32971 11.3196 3.37799L8.81821 4.50498C8.46489 4.66424 8.46489 5.22721 8.81821 5.38647Z" fill="currentColor" />
                <path d="M26.444 6.61692C27.5597 6.15447 28.0892 4.87516 27.6268 3.75949C27.1643 2.64382 25.885 2.11428 24.7694 2.57673C23.6537 3.03918 23.1242 4.31849 23.5866 5.43416C24.049 6.54983 25.3284 7.07937 26.444 6.61692Z" fill="currentColor" />
                <path d="M7.37707 17.5206C7.40519 17.0098 7.01387 16.5729 6.50302 16.5447C5.99217 16.5166 5.55525 16.9079 5.52712 17.4188C5.499 17.9296 5.89032 18.3665 6.40117 18.3947C6.91202 18.4228 7.34894 18.0315 7.37707 17.5206Z" fill="currentColor" />
                <path d="M46.8542 12.8587L48.7096 13.6947C48.7891 13.7306 48.852 13.8014 48.8838 13.8908L49.6265 15.9792C49.7314 16.2743 50.1025 16.2743 50.2074 15.9792L50.9501 13.8908C50.9819 13.8014 51.0449 13.7306 51.1244 13.6947L52.9798 12.8587C53.242 12.7406 53.242 12.3229 52.9798 12.2049L51.1244 11.369C51.0449 11.3333 50.982 11.2624 50.9501 11.173L50.2074 9.08459C50.1024 8.78951 49.7314 8.78951 49.6265 9.08459L48.8838 11.173C48.852 11.2624 48.7891 11.3333 48.7096 11.369L46.8542 12.2049C46.5921 12.3229 46.5921 12.7406 46.8542 12.8587Z" fill="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_5563_582">
                    <rect width="60" height="60" fill="white" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default Feeder