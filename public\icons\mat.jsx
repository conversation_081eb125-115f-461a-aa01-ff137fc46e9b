import React from 'react'

const Mat = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="65" height="65" viewBox="0 0 65 65" fill="none">
            <g clipPath="url(#clip0_5563_156)">
                <path d="M49.151 12.0898H15.8047C10.4997 12.0898 6.18372 16.4057 6.18372 21.7109V55.0573C6.18372 60.3624 10.4996 64.6783 15.8047 64.6783H49.1511C54.4561 64.6783 58.7721 60.3624 58.7721 55.0573V21.7109C58.7721 16.4057 54.4561 12.0898 49.151 12.0898ZM15.8047 14.6685H31.7043V20.9384C31.7043 21.3075 31.4041 21.6078 31.0349 21.6078H25.6728C24.4506 21.6078 23.4562 22.6022 23.4562 23.8244V29.0094C23.4562 30.2316 24.4506 31.2259 25.6728 31.2259H31.0349C31.4041 31.2259 31.7043 31.5261 31.7043 31.8953V37.7499H25.6728C24.4506 37.7499 23.4562 38.7443 23.4562 39.9665V45.1514C23.4562 45.5205 23.156 45.8208 22.7869 45.8208H17.4248C17.0556 45.8208 16.7554 45.5205 16.7554 45.1514V39.9665C16.7554 38.7443 15.7609 37.7499 14.5388 37.7499H8.76239V21.7109C8.76239 17.8276 11.9215 14.6685 15.8047 14.6685ZM8.76239 55.0571V39.2972H14.5388C14.9079 39.2972 15.2082 39.5974 15.2082 39.9666V45.1515C15.2082 46.3737 16.2026 47.3681 17.4248 47.3681H22.7869C24.0091 47.3681 25.0034 46.3737 25.0034 45.1515V39.9666C25.0034 39.5974 25.3036 39.2972 25.6728 39.2972H31.7043V45.1516C31.7043 46.3738 32.6988 47.3682 33.9209 47.3682H39.2829C39.6521 47.3682 39.9524 47.6685 39.9524 48.0376V53.2227C39.9524 53.5918 39.6521 53.8921 39.2829 53.8921H33.9209C32.6988 53.8921 31.7043 54.8865 31.7043 56.1087V62.0995H15.8047C11.9215 62.0995 8.76239 58.9404 8.76239 55.0571ZM49.151 62.0995H33.2515V56.1087C33.2515 55.7395 33.5518 55.4393 33.9209 55.4393H39.2829C40.5051 55.4393 41.4995 54.4448 41.4995 53.2227V48.0376C41.4995 46.8155 40.5051 45.821 39.2829 45.821H33.9209C33.5518 45.821 33.2515 45.5208 33.2515 45.1516V39.2972H39.2829C40.5051 39.2972 41.4995 38.3027 41.4995 37.0806V31.8955C41.4995 31.5264 41.7998 31.2261 42.169 31.2261H47.5311C47.9001 31.2261 48.2004 31.5264 48.2004 31.8955V37.0806C48.2004 38.3027 49.1948 39.2972 50.417 39.2972H56.1935V55.0571C56.1935 58.9404 53.0344 62.0995 49.151 62.0995ZM56.1935 37.75H50.417C50.0478 37.75 49.7476 37.4497 49.7476 37.0806V31.8955C49.7476 30.6734 48.7533 29.6789 47.5311 29.6789H42.169C40.9468 29.6789 39.9524 30.6734 39.9524 31.8955V37.0806C39.9524 37.4497 39.6521 37.75 39.2829 37.75H33.2515V31.8954C33.2515 30.6732 32.2571 29.6788 31.0349 29.6788H25.6728C25.3036 29.6788 25.0034 29.3786 25.0034 29.0095V23.8245C25.0034 23.4553 25.3036 23.1551 25.6728 23.1551H31.0349C32.2571 23.1551 33.2515 22.1607 33.2515 20.9385V14.6686H49.151C53.0342 14.6686 56.1935 17.8279 56.1935 21.711V37.75Z" fill="currentColor" />
                <path d="M11.5962 30.2622C11.6 30.972 12.1765 31.5446 12.8854 31.5446H12.8923C13.6045 31.5408 14.1786 30.9605 14.1748 30.2486C14.1724 29.8082 14.1694 29.3676 14.1665 28.9271C14.159 27.8221 14.1514 26.6794 14.1545 25.5585C14.1564 24.8466 13.5806 24.2676 12.8687 24.2657C12.1555 24.2606 11.5777 24.8392 11.5758 25.5514C11.5726 26.6845 11.5805 27.8334 11.588 28.9445C11.591 29.3838 11.5941 29.823 11.5962 30.2622Z" fill="currentColor" />
                <path d="M12.8191 22.0254C12.9542 22.0702 13.0913 22.0917 13.2262 22.0917C13.7663 22.0917 14.2697 21.7496 14.4495 21.209C14.6841 20.5034 15.221 20.0245 15.63 19.7471C16.2195 19.3475 16.3731 18.5456 15.9733 17.9565C15.5736 17.367 14.7714 17.2133 14.1826 17.6132C13.1227 18.3321 12.369 19.294 12.0026 20.3951C11.778 21.0707 12.1435 21.8007 12.8191 22.0254Z" fill="currentColor" />
                <path d="M51.6587 51.2031C51.2315 51.2031 50.885 51.5495 50.885 51.9768V53.4663C50.885 53.8935 51.2315 54.24 51.6587 54.24C52.0859 54.24 52.4323 53.8936 52.4323 53.4663V51.9768C52.4323 51.5495 52.086 51.2031 51.6587 51.2031Z" fill="currentColor" />
                <path d="M46.8851 44.0547C46.4579 44.0547 46.1116 44.401 46.1116 44.8282V46.3178C46.1116 46.7449 46.4579 47.0914 46.8851 47.0914C47.3123 47.0914 47.6588 46.7451 47.6588 46.3178V44.8282C47.6588 44.401 47.3123 44.0547 46.8851 44.0547Z" fill="currentColor" />
                <path d="M45.6085 55.6914C45.1813 55.6914 44.835 56.0377 44.835 56.4651V57.9547C44.835 58.3819 45.1813 58.7283 45.6085 58.7283C46.0357 58.7283 46.382 58.3819 46.382 57.9547V56.4651C46.382 56.0377 46.0357 55.6914 45.6085 55.6914Z" fill="currentColor" />
                <path d="M6.5863 8.07713L9.32214 6.84442C9.70871 6.67024 9.70871 6.05451 9.32214 5.88033L6.5863 4.64775C6.46912 4.59493 6.37632 4.49058 6.32947 4.35867L5.23425 1.27943C5.07949 0.844358 4.53232 0.844358 4.37769 1.27943L3.2826 4.35867C3.23575 4.49058 3.14295 4.59506 3.02577 4.64775L0.289929 5.88033C-0.0966431 6.05451 -0.0966431 6.67024 0.289929 6.84442L3.02577 8.07713C3.14295 8.12982 3.23575 8.23443 3.2826 8.3662L4.37769 11.4456C4.53245 11.8806 5.07962 11.8806 5.23425 11.4456L6.32934 8.3662C6.37619 8.23443 6.46912 8.12994 6.5863 8.07713Z" fill="currentColor" />
                <path d="M64.8396 13.7081C65.3454 12.4878 64.7662 11.0885 63.5459 10.5827C62.3256 10.0769 60.9263 10.6561 60.4205 11.8764C59.9147 13.0967 60.4939 14.496 61.7142 15.0018C62.9345 15.5076 64.3338 14.9284 64.8396 13.7081Z" fill="currentColor" />
                <path d="M16.2199 2.36135C16.7791 2.36135 17.2326 1.908 17.2326 1.34864C17.2326 0.789287 16.7792 0.335938 16.2199 0.335938C15.6606 0.335938 15.2072 0.789287 15.2072 1.34864C15.2072 1.908 15.6605 2.36135 16.2199 2.36135Z" fill="currentColor" />
                <path d="M48.3202 4.69439L50.3495 5.60871C50.4364 5.64781 50.5053 5.72525 50.5401 5.82313L51.3525 8.1074C51.4672 8.42999 51.8731 8.42999 51.9879 8.1074L52.8002 5.82313C52.835 5.72525 52.9038 5.64781 52.9908 5.60871L55.0201 4.69439C55.3068 4.56528 55.3068 4.10837 55.0201 3.97926L52.9908 3.06495C52.9038 3.02584 52.835 2.9484 52.8002 2.85052L51.9879 0.566255C51.8731 0.24354 51.4672 0.24354 51.3525 0.566255L50.5401 2.85052C50.5053 2.9484 50.4365 3.02584 50.3495 3.06495L48.3202 3.97926C48.0334 4.10837 48.0334 4.56515 48.3202 4.69439Z" fill="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_5563_156">
                    <rect width="65" height="65" fill="white" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default Mat