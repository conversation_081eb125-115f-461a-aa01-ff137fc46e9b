import React from 'react'

const Blocks = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
            <g clipPath="url(#clip0_5567_220)">
                <path d="M15.3478 23.2773H5.60605C4.55558 23.2773 3.70105 24.132 3.70105 25.1823V34.9241C3.70105 35.9745 4.55566 36.8291 5.60605 36.8291H15.3478C16.3982 36.8291 17.2528 35.9745 17.2528 34.9241V25.1823C17.2528 24.132 16.3982 23.2773 15.3478 23.2773ZM16.2929 34.9241C16.2929 35.4452 15.8689 35.8692 15.3478 35.8692H5.60605C5.08488 35.8692 4.66089 35.4452 4.66089 34.9241V25.1823C4.66089 24.6612 5.08496 24.2372 5.60605 24.2372H15.3478C15.8689 24.2372 16.2929 24.6612 16.2929 25.1823V34.9241Z" fill="currentColor" />
                <path d="M34.6909 20.1081H28.805C29.3249 19.4298 29.6349 18.5826 29.6349 17.664V5.82305C29.6349 3.60297 27.8287 1.79688 25.6087 1.79688H13.7677C11.5476 1.79688 9.74145 3.60297 9.74145 5.82305V17.6639C9.74145 18.5826 10.0514 19.4298 10.5712 20.108H4.55652C2.33645 20.108 0.530273 21.9141 0.530273 24.1343V35.9752C0.530273 38.1953 2.33637 40.0014 4.55652 40.0014H16.3974C17.7162 40.0014 18.8889 39.364 19.6237 38.3812C20.3585 39.364 21.5312 40.0014 22.85 40.0014H34.6909C36.9109 40.0014 38.7171 38.1953 38.7171 35.9752V24.1343C38.7171 21.9142 36.9109 20.1081 34.6909 20.1081ZM11.3412 5.82305C11.3412 4.48508 12.4297 3.39656 13.7677 3.39656H25.6086C26.9465 3.39656 28.035 4.48508 28.035 5.82305V17.6639C28.035 19.0019 26.9465 20.0904 25.6086 20.0904H13.7677C12.4297 20.0904 11.3412 19.0019 11.3412 17.6639V5.82305ZM19.6536 21.6902C19.6438 21.703 19.6334 21.7154 19.6237 21.7284C19.614 21.7154 19.6036 21.703 19.5938 21.6902H19.6536ZM16.3974 38.4017H4.55652C3.21855 38.4017 2.13004 37.3132 2.13004 35.9752V24.1343C2.13004 22.7963 3.21855 21.7078 4.55652 21.7078H16.3974C17.7354 21.7078 18.8239 22.7963 18.8239 24.1343V35.9752C18.8238 37.3132 17.7354 38.4017 16.3974 38.4017ZM37.1174 35.9752C37.1174 37.3131 36.0289 38.4016 34.6909 38.4016H22.85C21.5121 38.4016 20.4236 37.3131 20.4236 35.9752V24.1343C20.4236 22.7963 21.5121 21.7078 22.85 21.7078H34.6909C36.0289 21.7078 37.1174 22.7963 37.1174 24.1343V35.9752Z" fill="currentColor" />
                <path d="M33.6413 23.2773H23.8996C22.8492 23.2773 21.9946 24.132 21.9946 25.1823V34.9241C21.9946 35.9745 22.8492 36.8291 23.8996 36.8291H33.6413C34.6918 36.8291 35.5463 35.9745 35.5463 34.9241V25.1823C35.5463 24.132 34.6917 23.2773 33.6413 23.2773ZM34.5864 34.9241C34.5864 35.4452 34.1624 35.8692 33.6413 35.8692H23.8996C23.3785 35.8692 22.9545 35.4452 22.9545 34.9241V25.1823C22.9545 24.6612 23.3785 24.2372 23.8996 24.2372H33.6413C34.1625 24.2372 34.5865 24.6612 34.5865 25.1823V34.9241H34.5864Z" fill="currentColor" />
                <path d="M14.8174 18.5205H24.5591C25.6095 18.5205 26.4641 17.6659 26.4641 16.6155V6.87375C26.4641 5.82336 25.6095 4.96875 24.5591 4.96875H14.8174C13.767 4.96875 12.9124 5.82336 12.9124 6.87375V16.6155C12.9124 17.6659 13.767 18.5205 14.8174 18.5205ZM13.8722 6.87375C13.8722 6.35258 14.2962 5.92859 14.8174 5.92859H24.5591C25.0802 5.92859 25.5042 6.35258 25.5042 6.87375V16.6155C25.5042 17.1366 25.0802 17.5606 24.5591 17.5606H14.8174C14.2962 17.5606 13.8722 17.1366 13.8722 16.6155V6.87375Z" fill="currentColor" />
                <path d="M11.3997 26.0355C11.3969 26.0283 11.394 26.0209 11.3911 26.0137C11.2402 25.647 10.8867 25.4102 10.4901 25.4102C10.4898 25.4102 10.4895 25.4102 10.4893 25.4102C10.0924 25.4105 9.7388 25.648 9.58856 26.0155C9.58606 26.0215 9.58372 26.0276 9.58137 26.0337L6.69372 33.6152C6.53645 34.028 6.74372 34.4902 7.15653 34.6474C7.56942 34.8047 8.03145 34.5974 8.18872 34.1846L8.71169 32.8116H12.247L12.7643 34.1823C12.8852 34.5027 13.1896 34.7 13.5128 34.7C13.6066 34.6999 13.7021 34.6833 13.795 34.6482C14.2083 34.4922 14.417 34.0307 14.261 33.6174L11.3997 26.0355ZM9.32091 31.2118L10.4874 28.1491L11.6432 31.2118H9.32091Z" fill="currentColor" />
                <path d="M30.7287 29.4973C31.0414 29.0717 31.2267 28.547 31.2267 27.9795C31.2267 26.5627 30.0741 25.4102 28.6574 25.4102H26.4236C25.9819 25.4102 25.6238 25.7683 25.6238 26.21V29.751V29.7567V33.8999C25.6238 34.1127 25.7085 34.3166 25.8592 34.4668C26.0092 34.6161 26.2121 34.6998 26.4236 34.6998H26.4272C26.4478 34.6997 28.4888 34.6906 29.0475 34.6906C30.6299 34.6906 31.9172 33.4033 31.9172 31.8209C31.9171 30.8662 31.4481 30.0193 30.7287 29.4973ZM28.6573 27.0099C29.192 27.0099 29.6269 27.4448 29.6269 27.9795C29.6269 28.5141 29.192 28.9491 28.6573 28.9491C28.382 28.9494 27.5978 28.952 27.2234 28.9512V27.01H28.6573V27.0099ZM29.0474 33.0909C28.7156 33.0909 27.8671 33.0941 27.2234 33.0967V30.5533C27.6376 30.5505 28.6185 30.5508 29.0474 30.5509C29.7477 30.5509 30.3174 31.1207 30.3174 31.821C30.3175 32.5212 29.7477 33.0909 29.0474 33.0909Z" fill="currentColor" />
                <path d="M20.3994 16.3874C21.4184 16.3874 22.3321 16.0534 23.0417 15.4213C23.1811 15.2972 23.3133 15.1608 23.4347 15.0159C23.7184 14.6774 23.674 14.1729 23.3355 13.8891C22.997 13.6054 22.4926 13.6496 22.2086 13.9883C22.1369 14.0738 22.059 14.1541 21.9775 14.2268C21.5597 14.5989 21.0287 14.7877 20.3993 14.7877C18.7202 14.7877 17.3541 13.4216 17.3541 11.7426C17.3541 10.0635 18.7202 8.69742 20.3993 8.69742C21.0096 8.69742 21.5981 8.87711 22.1012 9.21695C22.4671 9.4643 22.9645 9.36805 23.2118 9.00195C23.459 8.63594 23.3629 8.13867 22.9969 7.89141C22.2283 7.37219 21.3301 7.09766 20.3994 7.09766C17.8382 7.09766 15.7545 9.18133 15.7545 11.7425C15.7546 14.3037 17.8383 16.3874 20.3994 16.3874Z" fill="currentColor" />
                <path d="M7.11922 29.5341V28.5694C7.11922 28.1276 6.76109 27.7695 6.31937 27.7695C5.87758 27.7695 5.51953 28.1277 5.51953 28.5694V29.5341C5.51953 29.9759 5.87766 30.3339 6.31937 30.3339C6.76109 30.3339 7.11922 29.9759 7.11922 29.5341Z" fill="currentColor" />
                <path d="M6.31937 27.0865C6.76117 27.0865 7.11922 26.7284 7.11922 26.2866V25.9327C7.11922 25.4909 6.76109 25.1328 6.31937 25.1328C5.87758 25.1328 5.51953 25.4909 5.51953 25.9327V26.2866C5.51953 26.7284 5.87766 27.0865 6.31937 27.0865Z" fill="currentColor" />
                <path d="M39.8182 13.4402C39.9493 12.6315 39.4 11.8697 38.5913 11.7386C37.7826 11.6075 37.0208 12.1568 36.8897 12.9655C36.7586 13.7741 37.3079 14.536 38.1166 14.667C38.9252 14.7981 39.6871 14.2489 39.8182 13.4402Z" fill="currentColor" />
                <path d="M5.47581 6.05471C5.81339 5.97491 6.02236 5.63653 5.94255 5.29891C5.86274 4.96129 5.52438 4.75228 5.1868 4.83208C4.84922 4.91187 4.64026 5.25026 4.72007 5.58788C4.79987 5.9255 5.13823 6.13451 5.47581 6.05471Z" fill="currentColor" />
                <path d="M5.93756 10.9553L4.24029 10.1906C4.16764 10.1578 4.11006 10.093 4.08092 10.0112L3.40154 8.10088C3.30561 7.83096 2.96615 7.83096 2.87014 8.10088L2.19076 10.0112C2.1617 10.093 2.10404 10.1578 2.03139 10.1906L0.334121 10.9553C0.0943555 11.0633 0.0943555 11.4453 0.334121 11.5534L2.03139 12.3181C2.10404 12.3508 2.16162 12.4156 2.19076 12.4974L2.87014 14.4078C2.96615 14.6776 3.30561 14.6776 3.40154 14.4078L4.08092 12.4974C4.10998 12.4156 4.16764 12.3509 4.24029 12.3181L5.93756 11.5534C6.17732 11.4454 6.17732 11.0633 5.93756 10.9553Z" fill="currentColor" />
                <path d="M33.8332 0.150176L34.3372 1.56729C34.3588 1.62791 34.4015 1.67604 34.4554 1.70033L35.7144 2.26752C35.8923 2.34768 35.8923 2.63104 35.7144 2.71119L34.4554 3.27846C34.4015 3.30275 34.3588 3.3508 34.3372 3.4115L33.8332 4.82861C33.7621 5.02877 33.5103 5.02877 33.439 4.82861L32.935 3.4115C32.9135 3.3508 32.8707 3.30275 32.8168 3.27846L31.5579 2.71119C31.38 2.63104 31.38 2.34768 31.5579 2.26752L32.8168 1.70033C32.8707 1.67604 32.9135 1.62799 32.935 1.56729L33.439 0.150176C33.5102 -0.0500586 33.762 -0.0500586 33.8332 0.150176Z" fill="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_5567_220">
                    <rect width="40" height="40" fill="currentColor" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default Blocks