import React from 'react'

const Crayon = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="120" height="120" viewBox="0 0 120 120" fill="none">
            <path d="M93.4055 23.9998C91.7851 21.5539 89.0661 20.0938 86.1319 20.0938C83.198 20.0938 80.4788 21.5539 78.8583 23.9998L72.522 33.564L66.1857 23.9998C64.5652 21.5539 61.8462 20.0938 58.9121 20.0938C55.9782 20.0938 53.259 21.5539 51.6387 23.9998L45.3024 33.564L38.9661 23.9998C37.3456 21.5539 34.6266 20.0938 31.6925 20.0938C28.7586 20.0938 26.0394 21.5539 24.4189 23.9998L17.9002 33.8391C16.9505 35.2723 16.4487 36.9385 16.4487 38.6579V111.271C16.4487 116.082 20.3628 119.996 25.1736 119.996H38.2114C41.1308 119.996 43.7178 118.554 45.3024 116.345C46.887 118.554 49.474 119.996 52.3934 119.996H65.4312C68.3506 119.996 70.9376 118.554 72.5222 116.345C74.1068 118.554 76.6939 119.996 79.6133 119.996H92.6511C97.4621 119.996 101.376 116.082 101.376 111.271V38.6581C101.376 36.9387 100.874 35.2723 99.9244 33.8394L93.4055 23.9998ZM21.1583 60.9052H42.2265V91.2242H21.1583V60.9052ZM48.3781 60.9052H69.4463V91.2242H48.3781V60.9052ZM75.5977 60.9052H96.6659V91.2242H75.5977V60.9052ZM76.2659 36.4405L82.7846 26.6009C83.5414 25.4586 84.7615 24.8033 86.1319 24.8033C87.5023 24.8033 88.7222 25.4586 89.4793 26.6009L95.9982 36.4405C96.4351 37.1 96.6659 37.8666 96.6659 38.6579V58.0794H75.5977V38.6579C75.5977 37.8669 75.8288 37.1 76.2659 36.4405ZM49.0461 36.4405L55.565 26.6009C56.322 25.4586 57.5419 24.8033 58.9123 24.8033C60.2827 24.8033 61.5029 25.4586 62.2597 26.6009L68.7786 36.4405C69.2154 37.1 69.4463 37.8666 69.4463 38.6579V58.0794H48.3781V38.6579C48.3781 37.8669 48.609 37.1 49.0461 36.4405ZM21.8262 36.4405L28.3451 26.6009C29.1022 25.4586 30.3221 24.8033 31.6925 24.8033C33.0629 24.8033 34.2828 25.4586 35.0398 26.6009L41.5587 36.4405C41.9956 37.1 42.2265 37.8666 42.2265 38.6579V58.0794H21.1583V38.6579C21.1583 37.8666 21.3894 37.1 21.8262 36.4405ZM38.2114 115.287H25.1736C22.9594 115.287 21.1583 113.485 21.1583 111.271V94.0503H42.2265V111.271C42.2267 113.485 40.4253 115.287 38.2114 115.287ZM65.431 115.287H52.3932C50.179 115.287 48.3779 113.485 48.3779 111.271V94.0503H69.4461V111.271C69.4463 113.485 67.6451 115.287 65.431 115.287ZM92.6508 115.287H79.613C77.3989 115.287 75.5977 113.485 75.5977 111.271V94.0503H96.6659V111.271C96.6661 113.485 94.865 115.287 92.6508 115.287Z" fill="currentColor" />
            <path d="M91.4773 73.125C90.697 73.125 90.0645 73.7576 90.0645 74.5378V77.3871C90.0645 78.1673 90.697 78.7999 91.4773 78.7999C92.2575 78.7999 92.8901 78.1673 92.8901 77.3871V74.5378C92.8903 73.7576 92.2577 73.125 91.4773 73.125Z" fill="currentColor" />
            <path d="M85.26 81.2188C84.4797 81.2188 83.8472 81.8513 83.8472 82.6316V85.4809C83.8472 86.2611 84.4797 86.8937 85.26 86.8937C86.0402 86.8937 86.6728 86.2611 86.6728 85.4809V82.6316C86.6728 81.8513 86.0402 81.2188 85.26 81.2188Z" fill="currentColor" />
            <path d="M63.446 75.2578C62.6658 75.2578 62.0332 75.8904 62.0332 76.6706V79.5199C62.0332 80.3002 62.6658 80.9327 63.446 80.9327C64.2263 80.9327 64.8588 80.3002 64.8588 79.5199V76.6706C64.8591 75.8904 64.2263 75.2578 63.446 75.2578Z" fill="currentColor" />
            <path d="M25.6604 53.3396C26.961 53.3396 28.0152 52.2854 28.0152 50.9848V46.1673C28.0152 44.8667 26.961 43.8125 25.6604 43.8125C24.3599 43.8125 23.3057 44.8667 23.3057 46.1673V50.9848C23.3057 52.2854 24.3599 53.3396 25.6604 53.3396Z" fill="currentColor" />
            <path d="M25.6604 66.1719C24.3599 66.1719 23.3057 67.2261 23.3057 68.5266V78.6791C23.3057 79.9796 24.3599 81.0338 25.6604 81.0338C26.961 81.0338 28.0152 79.9796 28.0152 78.6791V68.5266C28.0152 67.2261 26.961 66.1719 25.6604 66.1719Z" fill="currentColor" />
            <path d="M18.2742 8.99918L13.2773 6.74801C13.0634 6.65168 12.8939 6.4609 12.8081 6.21996L10.8082 0.595898C10.5255 -0.198633 9.52641 -0.198633 9.24375 0.595898L7.24359 6.21996C7.15805 6.46066 6.98836 6.65145 6.77437 6.74801L1.7775 8.99918C1.07156 9.31723 1.07156 10.442 1.7775 10.76L6.77437 13.0112C6.98836 13.1075 7.15781 13.2983 7.24359 13.5393L9.24375 19.1633C9.52641 19.9579 10.5258 19.9579 10.8082 19.1633L12.8084 13.5393C12.8939 13.2986 13.0636 13.1078 13.2776 13.0112L18.2745 10.76C18.9802 10.442 18.9802 9.31723 18.2742 8.99918Z" fill="currentColor" />
            <path d="M74.2634 16.3556C76.6127 15.8056 78.0713 13.4553 77.5214 11.106C76.9715 8.75673 74.6212 7.29806 72.2719 7.848C69.9226 8.39794 68.4639 10.7482 69.0138 13.0975C69.5638 15.4468 71.9141 16.9055 74.2634 16.3556Z" fill="currentColor" />
            <path d="M44.5816 17.9882C45.603 17.9882 46.4311 17.1602 46.4311 16.1385C46.4311 15.1169 45.603 14.2891 44.5816 14.2891C43.5602 14.2891 42.7322 15.1171 42.7322 16.1385C42.7322 17.1599 43.56 17.9882 44.5816 17.9882Z" fill="currentColor" />
            <path d="M118.359 28.824L114.653 27.154C114.494 27.0826 114.368 26.941 114.305 26.7624L112.821 22.5905C112.612 22.0011 111.87 22.0011 111.66 22.5905L110.177 26.7624C110.113 26.941 109.988 27.0826 109.829 27.154L106.122 28.824C105.599 29.06 105.599 29.8944 106.122 30.1301L109.829 31.8001C109.988 31.8715 110.113 32.0131 110.177 32.1917L111.66 36.3636C111.87 36.953 112.611 36.953 112.821 36.3636L114.305 32.1917C114.368 32.0131 114.494 31.8715 114.653 31.8001L118.359 30.1301C118.883 29.8941 118.883 29.0597 118.359 28.824Z" fill="currentColor" />
        </svg>
    )
}

export default Crayon