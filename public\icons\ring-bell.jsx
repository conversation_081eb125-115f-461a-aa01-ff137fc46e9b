import React from 'react'

const RingBell = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
            <g clipPath="url(#clip0_5594_2041)">
                <path d="M50.3767 28.1371L48.1485 27.8015C47.2082 27.6598 46.2687 27.8928 45.5034 28.4577C44.738 29.0226 44.2387 29.8516 44.097 30.792L43.5206 34.6183C43.3789 35.5588 43.6119 36.4982 44.1768 37.2634C44.3132 37.4482 44.4653 37.6171 44.6304 37.7697L43.1954 47.2959C43.1574 47.5486 42.9619 47.7433 42.709 47.7805L39.3996 48.2675V47.7682C39.3996 45.2169 37.3675 43.1325 34.8371 43.045C38.9687 39.7285 41.6189 34.6386 41.6189 28.9406C41.6189 18.9706 33.5076 10.8594 23.5376 10.8594C13.5677 10.8594 5.45654 18.9705 5.45654 28.9406C5.45654 34.6386 8.10674 39.7285 12.2383 43.045C9.70799 43.1325 7.67596 45.2169 7.67596 47.7682V55.2744C7.67596 57.8811 9.79658 60.0018 12.4033 60.0018H34.6723C37.2789 60.0018 39.3997 57.8812 39.3997 55.2744V54.4202L44.019 53.7153C46.8026 53.2905 48.9546 51.1344 49.374 48.3501L50.8303 38.6833C51.846 38.1861 52.6102 37.2154 52.791 36.015L53.3675 32.1887C53.509 31.2482 53.2762 30.3088 52.7112 29.5436C52.1464 28.7782 51.3174 28.2788 50.3767 28.1371ZM7.81494 28.9406C7.81494 20.2711 14.8682 13.2178 23.5378 13.2178C32.2073 13.2178 39.2607 20.2711 39.2607 28.9406C39.2607 37.6101 32.2074 44.6635 23.5378 44.6635C14.8681 44.6635 7.81494 37.6101 7.81494 28.9406ZM37.0413 55.2744C37.0413 56.5807 35.9785 57.6434 34.6723 57.6434H12.4033C11.0971 57.6434 10.0344 56.5807 10.0344 55.2744V47.7682C10.0344 46.462 11.0971 45.3992 12.4033 45.3992H16.0547C18.3363 46.4407 20.8703 47.0217 23.5378 47.0217C26.2052 47.0217 28.7392 46.4407 31.0208 45.3992H34.6723C35.9785 45.3992 37.0413 46.462 37.0413 47.7682V55.2744ZM47.0418 47.9989C46.7772 49.7555 45.4194 51.116 43.6631 51.384L39.3997 52.0346V50.6514L43.0523 50.1139C44.339 49.9249 45.3336 48.9336 45.5275 47.6475L46.8718 38.7243L48.4042 38.9552L47.0418 47.9989ZM51.0353 31.8372L50.4589 35.6636C50.3602 36.3192 49.7458 36.7722 49.0908 36.6734L46.8625 36.3377C46.5449 36.2899 46.265 36.1213 46.0743 35.8627C45.8835 35.6045 45.8049 35.2872 45.8527 34.9696L46.4292 31.1432C46.477 30.8258 46.6457 30.5458 46.9039 30.3552C47.1623 30.1645 47.479 30.0856 47.7971 30.1336L50.0254 30.4693C50.343 30.5171 50.6229 30.6857 50.8136 30.9441C51.0046 31.2024 51.0833 31.5196 51.0353 31.8372Z" fill="currentColor" />
                <path d="M17.5612 18.4038C17.7417 18.4038 17.9248 18.3622 18.0966 18.2745C18.5738 18.0309 19.0721 17.8172 19.5775 17.6398C20.1919 17.4241 20.5151 16.7509 20.2994 16.1365C20.0836 15.5221 19.4108 15.1987 18.7961 15.4147C18.1913 15.6271 17.595 15.8826 17.0239 16.1745C16.4439 16.4706 16.2139 17.1809 16.5102 17.761C16.7186 18.1689 17.1322 18.4038 17.5612 18.4038Z" fill="currentColor" />
                <path d="M15.0894 18.792C14.6296 18.3307 13.8829 18.3297 13.4218 18.7893C11.6641 20.5412 10.3866 22.7247 9.72768 25.1036C9.55378 25.7311 9.92163 26.3808 10.5492 26.5547C10.6545 26.584 10.7605 26.5978 10.8646 26.5978C11.3813 26.5978 11.8557 26.2554 12.0003 25.7331C12.5505 23.7471 13.6177 21.9235 15.0867 20.4593C15.5479 19.9998 15.5492 19.2531 15.0894 18.792Z" fill="currentColor" />
                <path d="M23.5377 33.1632C25.8679 33.1632 27.7638 31.2673 27.7638 28.9371C27.7638 26.6068 25.8679 24.7109 23.5377 24.7109C21.2074 24.7109 19.3115 26.6068 19.3115 28.9371C19.3115 31.2673 21.2074 33.1632 23.5377 33.1632ZM23.5377 26.126C25.0877 26.126 26.3487 27.387 26.3487 28.9371C26.3487 30.4871 25.0877 31.7482 23.5377 31.7482C21.9876 31.7482 20.7266 30.4871 20.7266 28.9371C20.7266 27.387 21.9876 26.126 23.5377 26.126Z" fill="currentColor" />
                <path d="M18.8565 37.1655C16.2209 35.67 14.4529 32.9718 14.1268 29.9481C14.0848 29.5595 13.7348 29.2788 13.3475 29.3205C12.959 29.3625 12.6781 29.7113 12.7199 30.0999C13.0949 33.5751 15.1278 36.6766 18.1583 38.3965C18.2684 38.459 18.3884 38.4887 18.5068 38.4887C18.7532 38.4887 18.9925 38.3598 19.1228 38.1302C19.3154 37.7902 19.1963 37.3584 18.8565 37.1655Z" fill="currentColor" />
                <path d="M23.5375 38.4007C23.1468 38.4007 22.8301 38.7175 22.8301 39.1082C22.8301 39.4989 23.1468 39.8156 23.5375 39.8156C29.5355 39.8156 34.4152 34.9359 34.4152 28.9379C34.4152 28.5472 34.0985 28.2305 33.7078 28.2305C33.3171 28.2305 33.0003 28.5472 33.0003 28.9379C33.0002 34.1558 28.7553 38.4007 23.5375 38.4007Z" fill="currentColor" />
                <path d="M28.8281 21.0907C29.3922 21.4719 29.9169 21.9153 30.3876 22.4088C30.5266 22.5546 30.713 22.6281 30.8997 22.6281C31.0752 22.6281 31.2509 22.5633 31.3879 22.4326C31.6707 22.163 31.6814 21.7151 31.4116 21.4324C30.871 20.8656 30.2683 20.356 29.6204 19.9182C29.2962 19.6994 28.8566 19.7846 28.638 20.1083C28.4191 20.4321 28.5044 20.8719 28.8281 21.0907Z" fill="currentColor" />
                <path d="M32.7038 48.5703C32.3131 48.5703 31.9963 48.8871 31.9963 49.2778V50.7045C31.9963 51.0952 32.3131 51.412 32.7038 51.412C33.0945 51.412 33.4113 51.0952 33.4113 50.7045V49.2778C33.4113 48.8872 33.0945 48.5703 32.7038 48.5703Z" fill="currentColor" />
                <path d="M29.6498 52.8281C29.2591 52.8281 28.9424 53.1449 28.9424 53.5356V54.9623C28.9424 55.353 29.2591 55.6698 29.6498 55.6698C30.0405 55.6698 30.3573 55.353 30.3573 54.9623V53.5356C30.3573 53.1449 30.0405 52.8281 29.6498 52.8281Z" fill="currentColor" />
                <path d="M24.1904 49.9961C23.7997 49.9961 23.4829 50.3129 23.4829 50.7036V52.1303C23.4829 52.521 23.7997 52.8378 24.1904 52.8378C24.5811 52.8378 24.8978 52.521 24.8978 52.1303V50.7037C24.8978 50.313 24.5811 49.9961 24.1904 49.9961Z" fill="currentColor" />
                <path d="M54.278 17.0767L51.7758 15.9494C51.6687 15.901 51.5838 15.8057 51.5409 15.6851L50.5393 12.8687C50.3977 12.4708 49.8973 12.4708 49.7559 12.8687L48.7543 15.6851C48.7114 15.8057 48.6265 15.901 48.5193 15.9494L46.0171 17.0767C45.6636 17.2359 45.6636 17.7991 46.0171 17.9584L48.5193 19.0857C48.6264 19.134 48.7114 19.2295 48.7543 19.3501L49.7559 22.1665C49.8974 22.5643 50.3978 22.5643 50.5393 22.1665L51.5409 19.3501C51.5838 19.2295 51.6686 19.134 51.7758 19.0857L54.278 17.9584C54.6315 17.7991 54.6315 17.2359 54.278 17.0767Z" fill="currentColor" />
                <path d="M44.8248 10.3309C46 10.0531 46.7275 8.87524 46.4497 7.70003C46.1719 6.52482 44.994 5.79733 43.8188 6.07513C42.6436 6.35292 41.9161 7.53082 42.1939 8.70603C42.4717 9.88124 43.6496 10.6087 44.8248 10.3309Z" fill="currentColor" />
                <path d="M19.852 6.15305C20.3636 6.15305 20.7782 5.73832 20.7782 5.22691C20.7782 4.71539 20.3636 4.30078 19.852 4.30078C19.3405 4.30078 18.9258 4.71539 18.9258 5.22691C18.9259 5.73844 19.3405 6.15305 19.852 6.15305Z" fill="currentColor" />
                <path d="M30.4513 3.99671L32.3074 4.83296C32.3868 4.86882 32.4499 4.9396 32.4816 5.02901L33.2246 7.11811C33.3296 7.41331 33.7007 7.41331 33.8057 7.11811L34.5487 5.02901C34.5806 4.93948 34.6435 4.8687 34.7229 4.83296L36.5791 3.99671C36.8412 3.87858 36.8412 3.46081 36.5791 3.34269L34.7229 2.50644C34.6435 2.47058 34.5806 2.39979 34.5487 2.31038L33.8057 0.221396C33.7007 -0.0737988 33.3296 -0.0737988 33.2246 0.221396L32.4816 2.31038C32.4499 2.39991 32.3868 2.47069 32.3074 2.50644L30.4513 3.34269C30.1891 3.46081 30.1891 3.87858 30.4513 3.99671Z" fill="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_5594_2041">
                    <rect width="60" height="60" fill="white" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default RingBell