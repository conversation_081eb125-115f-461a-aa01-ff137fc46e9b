import React from 'react'

const ThreeLine = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="35" height="28" viewBox="0 0 35 28" fill="none">
            <g clipPath="url(#clip0_5588_70)">
                <path d="M20.8226 22.9242C20.7839 23.9912 17.6139 23.2071 16.8677 23.1895C12.9097 22.5983 8.91005 22.1613 5.00267 21.3154C3.27295 20.995 2.63992 18.6936 3.98196 17.5451C4.19803 17.3477 4.45256 17.197 4.72959 17.1023C5.00663 17.0077 5.30021 16.9713 5.59193 16.9953C6.89055 17.1668 8.17126 17.4415 9.45735 17.7096C11.9642 18.1995 20.5978 19.9309 20.8226 22.9242Z" fill="currentColor" />
                <path d="M23.21 15.8114C22.4493 16.917 15.1699 11.9547 13.9275 11.2948C12.9486 10.69 11.9567 10.0989 10.9926 9.4702C9.87191 8.80057 8.84785 7.82588 9.51138 6.42946C10.93 3.96575 13.7368 6.38091 15.3339 7.41544C17.8411 9.24885 20.4614 11.0922 22.3329 13.6175C22.7118 14.1186 23.0137 14.6733 23.2287 15.2634C23.2625 15.3513 23.2783 15.4452 23.2751 15.5393C23.2719 15.6334 23.2497 15.726 23.21 15.8114Z" fill="currentColor" />
                <path d="M30.8012 7.11973C31.0152 8.20599 31.4508 11.136 30.4289 11.8364C30.3599 11.8913 30.2181 11.8997 30.1948 11.7895C28.2218 10.0496 26.5747 5.62717 25.9134 3.06065C25.4322 1.6956 26.9233 0.368015 28.2205 1.031C28.7855 1.28379 29.0252 1.83714 29.2184 2.38244C29.9018 3.90518 30.432 5.49207 30.8012 7.11973Z" fill="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_5588_70">
                    <rect width="20" height="30" fill="white" transform="translate(5.75671 27.7891) rotate(-106.728)" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default ThreeLine