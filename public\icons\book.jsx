import React from 'react'

const Book = ({ width = '40', height = '40' }) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 40 40" fill="none">
            <g clipPath="url(#clip0_5567_164)">
                <path d="M37.7775 10.5387H36.7726V10.0631C36.7726 9.21624 36.1996 8.47648 35.379 8.26437C30.2656 6.94265 25.0915 7.68523 19.994 10.4689C15.1622 7.91843 10.0212 7.18437 4.70607 8.28867C3.8492 8.46663 3.22732 9.2314 3.22732 10.1072V10.5388H2.2224C1.02045 10.5388 0.0424805 11.5168 0.0424805 12.7188V35.6211C0.0424805 36.823 1.02037 37.801 2.2224 37.801H15.2406C15.7383 37.801 16.1924 38.0273 16.4551 38.4065C17.1369 39.3901 18.4951 40.0012 19.9999 40.0012C21.5047 40.0012 22.863 39.3901 23.5447 38.4064C23.8075 38.0272 24.2615 37.8009 24.7593 37.8009H37.7774C38.9794 37.8009 39.9574 36.8229 39.9574 35.6209V12.7187C39.9575 11.5166 38.9795 10.5387 37.7775 10.5387ZM34.9862 9.78359C35.1141 9.81663 35.2034 9.93163 35.2034 10.0631V32.1198C35.1701 32.3396 35.0821 32.4102 34.8582 32.401C29.8956 31.4552 25.3156 32.0534 20.8569 34.2294C20.8375 34.251 20.7737 34.214 20.7847 34.1853V11.8241C25.5089 9.2553 30.2848 8.56851 34.9862 9.78359ZM4.79662 10.1071C4.79662 9.97116 4.89279 9.85257 5.02529 9.82507C10.0271 8.78577 14.6737 9.44179 19.2154 11.8312V34.1009C19.2265 34.1323 19.1587 34.1666 19.1399 34.1434V34.1435C14.9835 31.8992 10.4049 31.3251 5.14311 32.3883C5.02545 32.4122 4.94123 32.3612 4.90154 32.3287C4.8624 32.2966 4.7967 32.2253 4.7967 32.1073L4.79662 10.1071ZM38.3883 35.6209C38.3883 35.9576 38.1143 36.2316 37.7775 36.2316H24.7594C23.7471 36.2316 22.8108 36.7105 22.255 37.5126C21.9379 37.9701 21.1176 38.4319 20 38.4319C18.8824 38.4319 18.0621 37.9701 17.745 37.5125C17.1891 36.7104 16.2529 36.2316 15.2406 36.2316H2.2224C1.88561 36.2316 1.6117 35.9577 1.6117 35.6209V12.7187C1.6117 12.3819 1.88561 12.108 2.2224 12.108H3.2274V32.1073C3.2274 32.6662 3.47545 33.1895 3.90779 33.5433C4.34107 33.8979 4.90475 34.0374 5.45381 33.9265C10.3415 32.9388 14.574 33.4616 18.3944 35.5243V35.5244C18.6379 35.6558 18.9022 35.7213 19.1661 35.7213C19.4283 35.7213 19.6897 35.6559 19.9297 35.5269C20.1991 35.7115 20.5187 35.8054 20.8332 35.8054C21.0752 35.8053 21.3183 35.7505 21.545 35.6399C25.688 33.6179 29.9468 33.0627 34.5644 33.9426C35.112 34.047 35.6717 33.9034 36.1004 33.5486C36.5276 33.1952 36.7726 32.6744 36.7726 32.1198V12.108H37.7775C38.1142 12.108 38.3883 12.3819 38.3883 12.7187V35.6209Z" fill="currentColor" />
                <path d="M8.73242 28.0199H8.74109C8.765 28.0198 11.6041 28.0071 12.3924 28.0071C15.0012 28.0071 17.1236 25.8848 17.1236 23.2759C17.1236 21.9877 16.6039 20.7716 15.6853 19.8818C15.9971 19.2753 16.1604 18.6038 16.1604 17.9175C16.1604 15.5398 14.226 13.6055 11.8484 13.6055H8.7325C7.71578 13.6055 6.88867 14.4326 6.88867 15.4493V26.176C6.88867 26.663 7.08656 27.1392 7.43164 27.4827C7.77945 27.8292 8.24141 28.0199 8.73242 28.0199ZM7.83016 15.4493C7.83016 14.9518 8.23492 14.547 8.7325 14.547H11.8484C13.7069 14.547 15.2188 16.059 15.2188 17.9174C15.2188 18.5589 15.0361 19.1841 14.6904 19.7254C14.562 19.9265 14.6043 20.1919 14.7889 20.343C15.6743 21.068 16.182 22.137 16.182 23.2759C16.182 25.3655 14.482 27.0655 12.3924 27.0655C11.6025 27.0655 8.76008 27.0781 8.73242 27.0783C8.49211 27.0783 8.26609 26.9849 8.09586 26.8155C7.92695 26.6473 7.83016 26.4143 7.83016 26.1759V15.4493Z" fill="currentColor" />
                <path d="M10.1055 19.4862C10.5925 19.4873 11.5173 19.4833 11.8484 19.4834C12.7117 19.4834 13.4142 18.7809 13.4142 17.9174C13.4142 17.0541 12.7117 16.3516 11.8484 16.3516H10.1055C9.84555 16.3516 9.63477 16.5623 9.63477 16.8223V19.0155C9.63477 19.2755 9.84555 19.4862 10.1055 19.4862ZM10.5763 17.2931H11.8483C12.1926 17.2931 12.4726 17.5732 12.4726 17.9174C12.4726 18.2616 12.1925 18.5418 11.8483 18.5418C11.6105 18.5414 11.0265 18.5442 10.5763 18.5447V17.2931Z" fill="currentColor" />
                <path d="M10.1055 25.2662H10.1074C10.9614 25.2627 11.9728 25.2591 12.3924 25.2591C13.487 25.2591 14.3774 24.3687 14.3774 23.2741C14.3774 22.1796 13.487 21.2891 12.3924 21.2891C11.951 21.2891 10.5202 21.2884 10.1037 21.2914C9.84445 21.2924 9.63477 21.5029 9.63477 21.7622V24.7954C9.63477 24.9206 9.68469 25.0406 9.77328 25.1289C9.86156 25.2169 9.98102 25.2662 10.1055 25.2662ZM10.5763 22.2312L12.3923 22.2307C12.9677 22.2307 13.4358 22.6988 13.4358 23.2741C13.4358 23.8495 12.9677 24.3176 12.3923 24.3176C12.0476 24.3176 11.3043 24.32 10.5763 24.3228V22.2312Z" fill="currentColor" />
                <path d="M23.1937 20.9593C22.5943 22.0882 22.6466 23.4568 23.3343 24.535C23.3193 24.5827 23.2998 24.6389 23.2846 24.6823C23.1789 24.9852 23.0474 25.362 23.3227 25.6372C23.5974 25.9121 23.9783 25.7892 24.2843 25.6904C24.3327 25.6749 24.3962 25.6543 24.4475 25.64C25.0246 26.0025 25.6794 26.1843 26.335 26.1843C26.9046 26.1843 27.4746 26.0458 27.995 25.7704C28.4449 26.2318 29.0424 26.5128 29.6899 26.5128C29.7268 26.5128 29.7638 26.5118 29.801 26.51C30.4762 26.4767 31.0925 26.1486 31.5364 25.5862C31.9654 25.0427 32.1818 24.3418 32.1457 23.6125C32.1082 22.8529 31.6583 21.856 31.3603 21.2721C31.585 21.1489 31.7932 20.9947 31.9756 20.8123C32.4869 20.3011 32.7684 19.6214 32.7684 18.8984C32.7684 18.7572 32.7573 18.6176 32.7362 18.4806L33.502 18.1456C33.7402 18.0414 33.8489 17.7638 33.7446 17.5257C33.6403 17.2874 33.3628 17.1789 33.1247 17.283L32.43 17.5869C32.3088 17.3688 32.157 17.1659 31.9756 16.9845C31.7942 16.8031 31.5914 16.6513 31.3732 16.53L31.6771 15.8354C31.7813 15.5972 31.6726 15.3197 31.4344 15.2154C31.1964 15.1111 30.9186 15.2199 30.8145 15.4581L30.4795 16.2239C30.3425 16.2028 30.203 16.1917 30.0618 16.1917C29.3387 16.1917 28.659 16.4732 28.1478 16.9844C27.9662 17.1659 27.8118 17.3735 27.6887 17.5975C27.1043 17.2981 26.1146 16.8499 25.3586 16.8106C24.6308 16.7728 23.928 16.9875 23.3836 17.415C22.82 17.8576 22.4904 18.4731 22.4554 19.1481C22.4195 19.8396 22.7052 20.4822 23.1937 20.9593ZM27.1846 23.5682L25.3882 21.7718C26.3195 21.7182 27.5003 21.241 27.8879 21.0741C27.7196 21.4624 27.2411 22.6386 27.1846 23.5682ZM24.8581 24.7833C24.6725 24.6561 24.4683 24.666 24.2615 24.7157C24.3137 24.5121 24.3235 24.311 24.1943 24.1273C23.6764 23.3915 23.5795 22.4473 23.9156 21.6306L27.3286 25.0436C26.5227 25.3762 25.5946 25.2889 24.8581 24.7833ZM30.7975 25.0027C30.5244 25.3485 30.1542 25.5499 29.7546 25.5696C28.9068 25.6126 28.1724 24.8229 28.1223 23.8114V23.8114C28.0735 22.8239 28.9555 20.8235 29.4254 20.2045C29.4509 20.1829 29.4745 20.1617 29.4969 20.1409C30.0014 20.627 31.1548 22.6352 31.2054 23.6589C31.2302 24.1607 31.0853 24.6381 30.7975 25.0027ZM30.0617 17.1331C30.5332 17.1331 30.9764 17.3167 31.3099 17.65C31.6432 17.9835 31.8268 18.4268 31.8268 18.8983C31.8268 19.3698 31.6432 19.813 31.3099 20.1465C31.1882 20.2682 31.049 20.3707 30.8987 20.4519C30.3886 19.6297 30.0191 19.3134 29.7712 19.1933C29.6505 18.9443 29.3321 18.5732 28.5075 18.06C28.5881 17.9105 28.6903 17.7732 28.8135 17.65C29.1469 17.3167 29.5902 17.1331 30.0617 17.1331ZM23.3957 19.1968C23.4164 18.7974 23.6185 18.4275 23.965 18.1554C24.3304 17.8684 24.8084 17.7252 25.3098 17.7508C26.3335 17.804 28.3389 18.9625 28.8237 19.468C28.8031 19.4902 28.7821 19.5136 28.7606 19.5388C28.1412 20.0072 26.1413 20.8852 25.1497 20.8335H25.1498C24.1384 20.781 23.3515 20.0468 23.3957 19.1968Z" fill="currentColor" />
                <path d="M32.7198 27.0781C32.4598 27.0781 32.249 27.2889 32.249 27.5489V28.4983C32.249 28.7583 32.4598 28.9691 32.7198 28.9691C32.9798 28.9691 33.1906 28.7583 33.1906 28.4983V27.5489C33.1906 27.2889 32.9798 27.0781 32.7198 27.0781Z" fill="currentColor" />
                <path d="M29.5267 13.3284C29.7867 13.3284 29.9975 13.1177 29.9975 12.8577V11.9083C29.9975 11.6483 29.7867 11.4375 29.5267 11.4375C29.2667 11.4375 29.0559 11.6483 29.0559 11.9083V12.8577C29.0559 13.1177 29.2667 13.3284 29.5267 13.3284Z" fill="currentColor" />
                <path d="M28.5567 28.918C28.2967 28.918 28.0859 29.1288 28.0859 29.3888V30.3381C28.0859 30.5981 28.2967 30.8089 28.5567 30.8089C28.8167 30.8089 29.0275 30.5981 29.0275 30.3381V29.3888C29.0275 29.1288 28.8167 28.918 28.5567 28.918Z" fill="currentColor" />
                <path d="M0.411045 3.58516L2.07597 4.33531C2.14729 4.36742 2.20378 4.43102 2.2323 4.51125L2.89878 6.38516C2.99292 6.64992 3.32589 6.64992 3.42003 6.38516L4.08644 4.51125C4.11495 4.43102 4.17151 4.36742 4.24276 4.33531L5.90769 3.58516C6.14292 3.47922 6.14292 3.10445 5.90769 2.99844L4.24276 2.24836C4.17151 2.21617 4.11495 2.15266 4.08644 2.07242L3.42003 0.198516C3.32589 -0.0661719 2.99292 -0.0661719 2.89878 0.198516L2.2323 2.07242C2.20378 2.15266 2.14729 2.21625 2.07597 2.24836L0.411045 2.99844C0.175811 3.10445 0.175811 3.47922 0.411045 3.58516Z" fill="currentColor" />
                <path d="M19.0624 6.01195C19.8661 6.01195 20.5176 5.36047 20.5176 4.5568C20.5176 3.75305 19.8662 3.10156 19.0624 3.10156C18.2587 3.10156 17.6072 3.75305 17.6072 4.5568C17.6072 5.36047 18.2587 6.01195 19.0624 6.01195Z" fill="currentColor" />
                <path d="M13.4038 2.99422C13.7442 2.99422 14.0201 2.71828 14.0201 2.37797C14.0201 2.03758 13.7442 1.76172 13.4038 1.76172C13.0635 1.76172 12.7876 2.03766 12.7876 2.37797C12.7876 2.71836 13.0635 2.99422 13.4038 2.99422Z" fill="currentColor" />
                <path d="M35.5827 5.06973L36.8177 5.62613C36.8706 5.64996 36.9125 5.69707 36.9337 5.7566L37.4281 7.14668C37.4979 7.34309 37.7449 7.34309 37.8147 7.14668L38.3091 5.7566C38.3302 5.69707 38.3721 5.64996 38.425 5.62613L39.66 5.06973C39.8345 4.99113 39.8345 4.71316 39.66 4.63449L38.425 4.07809C38.3721 4.05426 38.3302 4.00707 38.3091 3.94754L37.8147 2.55746C37.7449 2.36105 37.4978 2.36105 37.4281 2.55746L36.9337 3.94754C36.9125 4.00707 36.8706 4.05426 36.8177 4.07809L35.5827 4.63449C35.4083 4.71309 35.4083 4.99106 35.5827 5.06973Z" fill="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_5567_164">
                    <rect width="40" height="40" fill="currentColor" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default Book