import React from 'react'
import AboutOne from '@/components/sections/abouts/aboutOne'
import HeaderOne from '@/components/sections/headers/headerOne'
import HeroOne from '@/components/sections/heros/heroOne'
import ServicesOne from '@/components/sections/services/servicesOne'
import SuccessProjectOne from '@/components/sections/successProjects/successProjectOne'
import Gallery from '@/components/sections/gallery/gallery'
// import AgeOne from '@/components/sections/studentsAge/ageOne'
import Teams from '@/components/sections/teams/teams'
import Programs from '@/components/sections/programs'
import FaqComp from '@/components/sections/faqComp'
import Testimonial from '@/components/sections/testimonial'
// import BlogsOne from '@/components/sections/blogs/blogsOne'
import NewsletterOne from '@/components/sections/newsletters/newsletterOne'
// import FooterOne from '@/components/sections/footers/footerOne'
import BlogsTwo from '@/components/sections/blogs/blogsTwo'
import AgeTwo from '@/components/sections/studentsAge/ageTwo'
import ExtraCurricular from '@/components/sections/extraCurricular'
import FooterTwo from '@/components/sections/footers/footerTwo'

// export const metadata = {
//   title: "Learning Circle",
//   description: "Ascent - Chindcare & Kids School Next.js and Tailwind CSS Template",
// };

const Home = () => {
  return (
    <>
    <HeaderOne/>
      <main>
        <HeroOne />
        <SuccessProjectOne/>
        <AboutOne isAboutpage={false}/>
        <Programs/>
        <Gallery/>
        <ServicesOne/>
        <ExtraCurricular/>
        <FaqComp/>
        <Teams/>
        <AgeTwo/>
        <Testimonial/>
        <BlogsTwo/>
        <NewsletterOne/>
      </main>
      <FooterTwo/>
    </>
  )
}

export default Home