@import url('https://fonts.googleapis.com/css2?family=Bubblegum+Sans&family=Jost:ital,wght@0,100..900;1,100..900&family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');

@import "tailwindcss";

@theme {
  /* Colors */
  --color-background: #fff;
  --color-foreground: #686868;
  --color-primary: #F7941E;
  --color-primary-foreground: #F7941E;
  --color-secondary: #1CBBB4;
  --color-secondary-foreground: #1CBBB4;
  --color-destructive: #ED145B;
  --color-destructive-foreground: #ED145B;
  --color-green: #73BE48;
  --color-green-foreground: #73BE48;
  --color-warm: #FFF0E5;
  --color-cream-foreground: #fff;
  --color-muted: #000;
  --color-muted-foreground: #000;
  --color-border: hsl(214.3 31.8% 91.4%);
  --color-input: hsl(214.3 31.8% 91.4%);
  --color-ring: hsl(222.2 84% 4.9%);

  /* Spacing */
  --spacing-2_5: 10px;
  --spacing-4_5: 18px;
  --spacing-7_5: 30px;
  --spacing-12_5: 50px;
  --spacing-15: 60px;
  --spacing-25: 100px;

  /* Border Radius */
  --radius: 0.5rem;
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  /* Box Shadows */
  --shadow-sm: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  --shadow-3xl: 0px 4.8px 24.4px -6px rgba(19, 16, 34, 0.10);
  --shadow-4xl: 0px 4.4px 20px -1px rgba(19, 16, 34, 0.05);

  /* Font Families */
  --font-family-bubblegum-sans: "Bubblegum Sans";
  --font-family-jost: "Jost";
  --font-family-nunito: "Nunito";

  /* Background Images */
  --background-image-testimonial-banner: url('/images/testimonial/bg-img.png');
  --background-image-newsletter-banner: url('/images/newsletter/bg-img.png');
  --background-image-newsletter-shap: url('/images/shapes/egg-shap.png');

  /* Container */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1290px;

  /* Animations */
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-left-right: left-right 2s linear infinite;
  --animate-left-right-2: left-right-2 4s linear infinite;
  --animate-up-down: up-down 2s linear infinite;
  --animate-skw: skw 2s linear infinite;
  --animate-expend-width-height: expend-width-height 2s linear infinite;

  /* Custom properties */
  --default-font-size: 16px;
  --default-line-height: 160%;
}

@layer base {
  .dark {
    --color-background: hsl(222.2 84% 4.9%);
    --color-foreground: hsl(210 40% 98%);

    --color-card: hsl(222.2 84% 4.9%);
    --color-card-foreground: hsl(210 40% 98%);

    --color-popover: hsl(222.2 84% 4.9%);
    --color-popover-foreground: hsl(210 40% 98%);

    --color-primary: hsl(210 40% 98%);
    --color-primary-foreground: hsl(222.2 47.4% 11.2%);

    --color-secondary: hsl(217.2 32.6% 17.5%);
    --color-secondary-foreground: hsl(210 40% 98%);

    --color-muted: hsl(217.2 32.6% 17.5%);
    --color-muted-foreground: hsl(215 20.2% 65.1%);

    --color-accent: hsl(217.2 32.6% 17.5%);
    --color-accent-foreground: hsl(210 40% 98%);

    --color-destructive: hsl(0 62.8% 30.6%);
    --color-destructive-foreground: hsl(210 40% 98%);

    --color-border: hsl(217.2 32.6% 17.5%);
    --color-input: hsl(217.2 32.6% 17.5%);
    --color-ring: hsl(212.7 26.8% 83.9%);
  }
}

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-background text-foreground;
    font-size: var(--default-font-size);
    font-family: Nunito;
    color: var(--muted-foreground);
    line-height: var(--default-line-height);
    background-color: var(--background);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: Jost;
  }

  p {
    color: var(--foreground);

  }
}

/* Custom Utility Classes */
@layer utilities {
  .stroke-primary {
    -webkit-text-fill-color: var(--color-cream-foreground);
    -webkit-text-stroke-width: 2px;
    -webkit-text-stroke-color: #686868;
  }

.container {
    max-width: 1290px;
    margin: 0 auto;
    padding-inline:1.5rem;
  }
  .container-xl {
    max-width: 1290px;
    margin: 0 auto;
    padding-inline:1.5rem;
  }
  .container-lg {
    max-width: 1024px;
    margin: 0 auto;
    padding-inline:1.5rem;
  }
  .container-md {
    max-width: 768px;
    margin: 0 auto;
    padding-inline:1.5rem;
  }
  .container-sm {
    max-width: 640px;
    margin: 0 auto;
    padding-inline:1.5rem;
  }

  /* Spacing Utilities */
  .space-2\.5 { margin: var(--spacing-2_5); }
  .space-4\.5 { margin: var(--spacing-4_5); }
  .space-7\.5 { margin: var(--spacing-7_5); }
  .space-12\.5 { margin: var(--spacing-12_5); }
  .space-15 { margin: var(--spacing-15); }
  .space-25 { margin: var(--spacing-25); }

  .p-2\.5 { padding: var(--spacing-2_5); }
  .p-4\.5 { padding: var(--spacing-4_5); }
  .p-7\.5 { padding: var(--spacing-7_5); }
  .p-12\.5 { padding: var(--spacing-12_5); }
  .p-15 { padding: var(--spacing-15); }
  .p-25 { padding: var(--spacing-25); }

  .pt-2\.5 { padding-top: var(--spacing-2_5); }
  .pt-4\.5 { padding-top: var(--spacing-4_5); }
  .pt-7\.5 { padding-top: var(--spacing-7_5); }
  .pt-12\.5 { padding-top: var(--spacing-12_5); }
  .pt-15 { padding-top: var(--spacing-15); }
  .pt-25 { padding-top: var(--spacing-25); }

  .pb-2\.5 { padding-bottom: var(--spacing-2_5); }
  .pb-4\.5 { padding-bottom: var(--spacing-4_5); }
  .pb-7\.5 { padding-bottom: var(--spacing-7_5); }
  .pb-12\.5 { padding-bottom: var(--spacing-12_5); }
  .pb-15 { padding-bottom: var(--spacing-15); }
  .pb-25 { padding-bottom: var(--spacing-25); }

  .pl-2\.5 { padding-left: var(--spacing-2_5); }
  .pl-4\.5 { padding-left: var(--spacing-4_5); }
  .pl-7\.5 { padding-left: var(--spacing-7_5); }
  .pl-12\.5 { padding-left: var(--spacing-12_5); }
  .pl-15 { padding-left: var(--spacing-15); }
  .pl-25 { padding-left: var(--spacing-25); }

  .pr-2\.5 { padding-right: var(--spacing-2_5); }
  .pr-4\.5 { padding-right: var(--spacing-4_5); }
  .pr-7\.5 { padding-right: var(--spacing-7_5); }
  .pr-12\.5 { padding-right: var(--spacing-12_5); }
  .pr-15 { padding-right: var(--spacing-15); }
  .pr-25 { padding-right: var(--spacing-25); }

  .px-2\.5 { padding-left: var(--spacing-2_5); padding-right: var(--spacing-2_5); }
  .px-4\.5 { padding-left: var(--spacing-4_5); padding-right: var(--spacing-4_5); }
  .px-7\.5 { padding-left: var(--spacing-7_5); padding-right: var(--spacing-7_5); }
  .px-12\.5 { padding-left: var(--spacing-12_5); padding-right: var(--spacing-12_5); }
  .px-15 { padding-left: var(--spacing-15); padding-right: var(--spacing-15); }
  .px-25 { padding-left: var(--spacing-25); padding-right: var(--spacing-25); }

  .py-2\.5 { padding-top: var(--spacing-2_5); padding-bottom: var(--spacing-2_5); }
  .py-4\.5 { padding-top: var(--spacing-4_5); padding-bottom: var(--spacing-4_5); }
  .py-7\.5 { padding-top: var(--spacing-7_5); padding-bottom: var(--spacing-7_5); }
  .py-12\.5 { padding-top: var(--spacing-12_5); padding-bottom: var(--spacing-12_5); }
  .py-15 { padding-top: var(--spacing-15); padding-bottom: var(--spacing-15); }
  .py-25 { padding-top: var(--spacing-25); padding-bottom: var(--spacing-25); }

  /* Margin Utilities */
  .m-2\.5 { margin: var(--spacing-2_5); }
  .m-4\.5 { margin: var(--spacing-4_5); }
  .m-7\.5 { margin: var(--spacing-7_5); }
  .m-12\.5 { margin: var(--spacing-12_5); }
  .m-15 { margin: var(--spacing-15); }
  .m-25 { margin: var(--spacing-25); }

  .mt-2\.5 { margin-top: var(--spacing-2_5); }
  .mt-4\.5 { margin-top: var(--spacing-4_5); }
  .mt-7\.5 { margin-top: var(--spacing-7_5); }
  .mt-12\.5 { margin-top: var(--spacing-12_5); }
  .mt-15 { margin-top: var(--spacing-15); }
  .mt-25 { margin-top: var(--spacing-25); }

  .mb-2\.5 { margin-bottom: var(--spacing-2_5); }
  .mb-4\.5 { margin-bottom: var(--spacing-4_5); }
  .mb-7\.5 { margin-bottom: var(--spacing-7_5); }
  .mb-12\.5 { margin-bottom: var(--spacing-12_5); }
  .mb-15 { margin-bottom: var(--spacing-15); }
  .mb-25 { margin-bottom: var(--spacing-25); }

  .ml-2\.5 { margin-left: var(--spacing-2_5); }
  .ml-4\.5 { margin-left: var(--spacing-4_5); }
  .ml-7\.5 { margin-left: var(--spacing-7_5); }
  .ml-12\.5 { margin-left: var(--spacing-12_5); }
  .ml-15 { margin-left: var(--spacing-15); }
  .ml-25 { margin-left: var(--spacing-25); }

  .mr-2\.5 { margin-right: var(--spacing-2_5); }
  .mr-4\.5 { margin-right: var(--spacing-4_5); }
  .mr-7\.5 { margin-right: var(--spacing-7_5); }
  .mr-12\.5 { margin-right: var(--spacing-12_5); }
  .mr-15 { margin-right: var(--spacing-15); }
  .mr-25 { margin-right: var(--spacing-25); }

  .mx-2\.5 { margin-left: var(--spacing-2_5); margin-right: var(--spacing-2_5); }
  .mx-4\.5 { margin-left: var(--spacing-4_5); margin-right: var(--spacing-4_5); }
  .mx-7\.5 { margin-left: var(--spacing-7_5); margin-right: var(--spacing-7_5); }
  .mx-12\.5 { margin-left: var(--spacing-12_5); margin-right: var(--spacing-12_5); }
  .mx-15 { margin-left: var(--spacing-15); margin-right: var(--spacing-15); }
  .mx-25 { margin-left: var(--spacing-25); margin-right: var(--spacing-25); }

  .my-2\.5 { margin-top: var(--spacing-2_5); margin-bottom: var(--spacing-2_5); }
  .my-4\.5 { margin-top: var(--spacing-4_5); margin-bottom: var(--spacing-4_5); }
  .my-7\.5 { margin-top: var(--spacing-7_5); margin-bottom: var(--spacing-7_5); }
  .my-12\.5 { margin-top: var(--spacing-12_5); margin-bottom: var(--spacing-12_5); }
  .my-15 { margin-top: var(--spacing-15); margin-bottom: var(--spacing-15); }
  .my-25 { margin-top: var(--spacing-25); margin-bottom: var(--spacing-25); }

  /* Background Image Utilities */
  .bg-testimonial-banner { background-image: var(--background-image-testimonial-banner); }
  .bg-newsletter-banner { background-image: var(--background-image-newsletter-banner); }
  .bg-newsletter-shap { background-image: var(--background-image-newsletter-shap); }

  /* Font Family Utilities */
  .font-bubblegum-sans { font-family: var(--font-family-bubblegum-sans); }
  .font-jost { font-family: var(--font-family-jost); }
  .font-nunito { font-family: var(--font-family-nunito); }

  /* Box Shadow Utilities */
  .shadow-sm { box-shadow: var(--shadow-sm); }
  .shadow-3xl { box-shadow: var(--shadow-3xl); }
  .shadow-4xl { box-shadow: var(--shadow-4xl); }

  /* Border Radius Utilities */
  .rounded-lg { border-radius: var(--radius-lg); }
  .rounded-md { border-radius: var(--radius-md); }
  .rounded-sm { border-radius: var(--radius-sm); }

  /* Animation Utilities */
  .animate-accordion-down { animation: var(--animate-accordion-down); }
  .animate-accordion-up { animation: var(--animate-accordion-up); }
  .animate-left-right { animation: var(--animate-left-right); }
  .animate-left-right-2 { animation: var(--animate-left-right-2); }
  .animate-up-down { animation: var(--animate-up-down); }
  .animate-skw { animation: var(--animate-skw); }
  .animate-expend-width-height { animation: var(--animate-expend-width-height); }
}

/* Keyframes for animations */
@keyframes accordion-down {
  from { height: 0; }
  to { height: var(--radix-accordion-content-height); }
}

@keyframes accordion-up {
  from { height: var(--radix-accordion-content-height); }
  to { height: 0; }
}

@keyframes left-right {
  50% { transform: translateX(14px); }
}

@keyframes left-right-2 {
  50% { transform: translateX(-40px); }
}

@keyframes up-down {
  50% { transform: translateY(-10px); }
}

@keyframes skw {
  50% { transform: skewX(5deg); }
}

@keyframes expend-width-height {
  100% {
    width: 56%;
    height: 56%;
  }
}

@layer components {

  /* ------- card hover image slide up start -------- */
  .image-layer-hover {
    background-size: cover;
    width: 25%;
    height: 100%;
    transition: 0.5s;
  }

  .image-layer-hover:nth-child(1) {
    background-position: 0;
    transition-delay: 0;
  }

  .image-layer-hover:nth-child(2) {
    background-position: 33.33%;
    transition-delay: 0.1s;
  }

  .image-layer-hover:nth-child(3) {
    background-position: 66.66%;
    transition-delay: 0.2s;
  }

  .image-layer-hover:nth-child(4) {
    background-position: 100%;
    transition-delay: 0.3s;
  }

  .layer-card:hover .image-layer-hover {
    transform: translateY(-100%);
  }

  /* ------- card hover image slide up end -------- */
}


/* service swiper pagination */
.services .swiper-pagination-bullets.swiper-pagination-horizontal {
  left: 0;
  top: 0;
  width: auto;
  display: flex;
  gap: 10px;
}

.services .swiper-pagination-bullet {
  height: 5px;
  width: 15px;
  border-radius: 10px;
  display: block;
  background-color: #F2F2F2;
}

.services .swiper-pagination-bullet-active {
  width: 33px;
  background-color: var(--secondary);
}

/* service swiper pagination */