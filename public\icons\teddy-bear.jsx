import React from 'react'

const TeddyBear = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="65" height="65" viewBox="0 0 65 65" fill="none">
            <g clipPath="url(#clip0_5563_195)">
                <path d="M64.258 19.0661C63.7512 17.3842 62.6197 16.0004 61.0721 15.1697C58.7725 13.9352 56.0058 14.1886 53.9864 15.7234C52.2503 13.1356 49.8281 10.9492 46.918 9.38708C44.0073 7.82454 40.8459 7.01382 37.7292 6.99693C37.8851 4.48098 36.5708 2.01644 34.2773 0.785244C32.7297 -0.045664 30.9512 -0.223906 29.2692 0.282764C27.5873 0.789688 26.2035 1.92109 25.3728 3.46878C23.9994 6.02714 24.4879 9.18764 26.5007 11.2139C25.4666 12.2066 24.5905 13.3547 23.9009 14.6394C22.6414 16.9854 22.1039 19.5979 22.2952 22.2702L17.7414 19.7473C16.5574 19.0914 15.1887 18.9356 13.8877 19.3092C12.5867 19.6827 11.509 20.5403 10.853 21.7244C9.4988 24.1685 10.3857 27.2585 12.8299 28.6126L19.9512 32.5578C19.2238 33.448 18.5817 34.3995 18.0459 35.3976C16.9784 37.3862 16.3971 39.4187 16.3081 41.4209L12.0787 51.7097C11.5722 52.9649 11.5848 54.3422 12.1143 55.588C12.6437 56.8337 13.6266 57.7986 14.8819 58.305C15.5013 58.555 16.1414 58.6732 16.7717 58.6732C18.7779 58.6732 20.6817 57.4736 21.4772 55.502L22.6035 52.7106C22.866 52.8726 23.1346 53.0281 23.4103 53.1761C24.7193 53.8788 26.0818 54.3695 27.4529 54.6438L28.8599 61.0234C29.3809 63.3861 31.4819 65.0002 33.8078 65C34.168 64.9999 34.534 64.9613 34.8998 64.8806C37.6284 64.2787 39.3587 61.5693 38.7569 58.8408L37.3456 52.4415C38.6604 51.4389 39.8061 50.1477 40.7505 48.5948L42.1993 51.4741C42.8076 52.6833 43.8504 53.5831 45.1357 54.0079C46.4161 54.4316 47.7922 54.3331 49.0036 53.7232C50.2127 53.1149 51.1125 52.072 51.5373 50.7867C51.9621 49.5016 51.8609 48.128 51.2527 46.9189L46.9078 38.284C50.5843 37.0978 53.5155 34.7202 55.2585 31.4734C55.9482 30.1889 56.4209 28.8245 56.6769 27.4143C57.1044 27.5 57.5346 27.5432 57.9615 27.5432C60.3194 27.5432 62.5783 26.2671 63.7555 24.0742C64.5864 22.5266 64.7649 20.748 64.258 19.0661ZM27.6758 4.70505C28.1764 3.77258 29.0102 3.09097 30.0234 2.78552C31.0365 2.4802 32.1084 2.5876 33.0409 3.0883C34.5285 3.88696 35.3329 5.54561 35.0847 7.17683C34.6454 7.23979 34.2088 7.31838 33.7758 7.41461C31.8528 7.84168 30.0995 8.57407 28.5697 9.5718C27.1792 8.3679 26.8011 6.33437 27.6758 4.70505ZM13.1394 22.9909C13.4569 22.4177 13.9789 22.0024 14.6088 21.8215C15.2389 21.6405 15.9014 21.7161 16.4746 22.0336L22.9062 25.5967C23.0315 26.0193 23.1739 26.4413 23.3356 26.8619C23.582 27.5032 23.8666 28.1293 24.1868 28.7379C23.3529 29.2858 22.5648 29.9032 21.8294 30.5749C21.7095 30.531 21.5924 30.4787 21.4798 30.4164L14.0965 26.3261C12.9132 25.6706 12.4837 24.1745 13.1394 22.9909ZM19.0534 54.5239C18.5473 55.7785 17.1147 56.3874 15.8599 55.8811C15.2522 55.6358 14.7762 55.1687 14.5199 54.5655C14.2636 53.9624 14.2575 53.2956 14.4996 52.6956L17.1197 46.3218C17.8216 48.1124 18.9574 49.733 20.4411 51.085L19.0534 54.5239ZM36.2045 59.4036C36.4959 60.7246 35.6581 62.0366 34.3371 62.3279C33.0161 62.6187 31.7039 61.7814 31.4126 60.4604L30.1863 54.9004C31.2177 54.8872 32.2374 54.7492 33.2244 54.4801C33.8301 54.3149 34.4158 54.1043 34.9801 53.8511L36.2045 59.4036ZM32.5367 51.9583C30.0267 52.6427 27.2249 52.257 24.6467 50.8732C22.0687 49.4892 20.1992 47.3668 19.3826 44.8968C18.5176 42.28 18.8517 39.4228 20.3489 36.6339C21.5557 34.3859 23.3853 32.4048 25.5701 30.9579C26.3394 32.0163 27.2267 33.001 28.217 33.8964C26.5484 34.7158 25.0758 36.1088 24.1794 37.7786C23.2528 39.5048 23.0546 41.2993 23.6063 42.9682C24.1233 44.5321 25.2849 45.8643 26.877 46.719C27.9452 47.2926 29.0854 47.5858 30.2002 47.5858C30.7468 47.5856 31.2875 47.5152 31.8106 47.3726C33.5064 46.9102 34.8925 45.7536 35.8192 44.0273C36.7155 42.3576 37.063 40.361 36.8241 38.5177C38.1175 38.8485 39.4285 39.0418 40.7356 39.0982C40.737 41.7189 40.0968 44.3392 38.8898 46.5875C37.3924 49.376 35.1956 51.2334 32.5367 51.9583ZM35.1365 38.0043C35.5418 39.6932 35.2879 41.7008 34.4372 43.2853C33.7192 44.6229 32.6683 45.513 31.398 45.8593C30.1979 46.187 28.8556 46.0013 27.6188 45.3372C25.4098 44.1513 23.9169 41.5834 25.5613 38.5203C26.4118 36.936 27.9446 35.615 29.5762 35.0196C30.4094 35.6483 31.2997 36.22 32.2417 36.7257C33.1836 37.2313 34.152 37.6574 35.1365 38.0043ZM49.0557 49.9665C48.8501 50.5888 48.4144 51.0937 47.8289 51.3883C47.2434 51.6828 46.5784 51.7315 45.9561 51.5261C45.3339 51.3205 44.8288 50.8848 44.5343 50.2992L42.1814 45.6232C42.9562 43.5358 43.3591 41.2785 43.3498 39.0243C43.6656 38.9923 43.98 38.9517 44.2932 38.9028L48.9179 48.0938C49.2124 48.6793 49.2613 49.3443 49.0557 49.9665ZM52.9557 30.2371C51.3434 33.2403 48.4531 35.3391 44.8172 36.1466C43.8852 36.3537 42.9324 36.4697 41.9723 36.4972C39.1116 36.5785 36.1763 35.8713 33.4781 34.4229C30.8088 32.9898 28.6169 30.9625 27.1054 28.5675C26.5772 27.7296 26.1284 26.8444 25.7756 25.9245C24.4395 22.4479 24.5918 18.8791 26.2039 15.8758C27.8162 12.8726 30.7066 10.7738 34.3425 9.96624C35.4181 9.72731 36.5203 9.60925 37.6315 9.60925C40.3507 9.60925 43.1227 10.3165 45.6816 11.6901C49.2863 13.6253 52.0216 16.6433 53.3841 20.1884C54.7201 23.665 54.5679 27.2336 52.9557 30.2371ZM61.4527 22.8378C60.5671 24.4876 58.6669 25.276 56.9027 24.7824C56.8891 22.956 56.5306 21.0896 55.824 19.2508C55.6649 18.8368 55.4892 18.4293 55.2989 18.0285C56.5295 16.9093 58.345 16.6724 59.8358 17.4729C60.7682 17.9734 61.4499 18.8073 61.7554 19.8205C62.0607 20.8337 61.9533 21.9054 61.4527 22.8378Z" fill="currentColor" />
                <path d="M41.0947 20.2332C39.4719 19.3621 37.6549 19.0736 35.9774 19.4205C34.249 19.7782 32.8741 20.763 32.106 22.1937C30.5468 25.0984 32.0418 28.9452 35.4388 30.7688C36.6378 31.4124 37.9156 31.7194 39.1363 31.7194C41.3738 31.7194 43.4184 30.6877 44.4273 28.8084C45.9865 25.9036 44.4915 22.0568 41.0947 20.2332ZM43.0455 28.0665C41.8951 30.2092 38.8155 30.8014 36.1804 29.387C33.5454 27.9724 32.3374 25.0782 33.4877 22.9355C34.0324 21.9212 35.0295 21.2181 36.2953 20.9561C36.6541 20.8819 37.0217 20.8451 37.3933 20.8451C38.3866 20.8451 39.4073 21.1073 40.3527 21.6148C42.9879 23.0296 44.1959 25.9238 43.0455 28.0665Z" fill="currentColor" />
                <path d="M33.3752 18.221C33.8307 17.3725 33.5122 16.3155 32.6636 15.8601C31.8152 15.4046 30.7581 15.7231 30.3027 16.5717C29.8472 17.4201 30.1658 18.4772 31.0143 18.9326C31.8627 19.3881 32.9197 19.0695 33.3752 18.221Z" fill="currentColor" />
                <path d="M49.4009 24.8445C48.5525 24.3889 47.4954 24.7075 47.04 25.556C46.5845 26.4045 46.9031 27.4616 47.7516 27.917C48.6 28.3725 49.6571 28.054 50.1125 27.2054C50.5679 26.357 50.2494 25.3 49.4009 24.8445Z" fill="currentColor" />
                <path d="M40.1536 23.2809C40.11 23.0473 39.9629 22.8462 39.7535 22.7339C39.5442 22.6214 39.2952 22.6102 39.0766 22.7028L35.9133 24.0438C35.5146 24.2129 35.3285 24.6731 35.4974 25.0718C35.6664 25.4707 36.1267 25.6562 36.5254 25.4877L38.7907 24.5273L39.242 26.9462C39.3124 27.3234 39.6418 27.5867 40.012 27.5867C40.0598 27.5867 40.1082 27.5823 40.1567 27.5732C40.5824 27.4937 40.8631 27.0843 40.7838 26.6585L40.1536 23.2809Z" fill="currentColor" />
                <path d="M45.0188 14.0568C43.8907 13.1881 42.6236 12.5485 41.2525 12.1561C40.5588 11.9579 39.8351 12.359 39.6365 13.0529C39.4378 13.7468 39.8394 14.4705 40.5334 14.669C41.5857 14.9703 42.5583 15.4609 43.4239 16.1277C43.6616 16.3108 43.9421 16.3993 44.2204 16.3993C44.6121 16.3993 44.9994 16.2239 45.2567 15.8897C45.6971 15.318 45.5906 14.4974 45.0188 14.0568Z" fill="currentColor" />
                <path d="M35.2594 14.6856C35.6092 14.6801 35.9031 14.5288 36.2681 14.4799C36.9784 14.3514 37.4499 13.6712 37.3212 12.9612C37.1926 12.2507 36.5125 11.779 35.8023 11.908C35.4744 11.9674 35.1731 12.0391 34.8815 12.1273C34.1906 12.3363 33.8 13.0658 34.009 13.7567C34.1798 14.3213 34.6984 14.6856 35.2594 14.6856Z" fill="currentColor" />
                <path d="M12.1633 7.66286L13.2734 10.7843C13.4302 11.2253 13.9848 11.2253 14.1416 10.7843L15.2517 7.66286C15.2992 7.52918 15.3934 7.4233 15.5121 7.36985L18.2852 6.12038C18.677 5.94391 18.677 5.31956 18.2852 5.14309L15.5121 3.89362C15.3934 3.84017 15.2992 3.73417 15.2517 3.60061L14.1416 0.479214C13.9848 0.0381787 13.4302 0.0381787 13.2734 0.479214L12.1633 3.60061C12.1158 3.73429 12.0216 3.84017 11.9029 3.89362L9.12977 5.14309C8.73799 5.31956 8.73799 5.94391 9.12977 6.12038L11.9029 7.36985C12.0216 7.4233 12.1157 7.52918 12.1633 7.66286Z" fill="currentColor" />
                <path d="M53.7267 5.96515C55.0654 5.96515 56.1506 4.87995 56.1506 3.54123C56.1506 2.20251 55.0654 1.11719 53.7267 1.11719C52.3879 1.11719 51.3027 2.20238 51.3027 3.54123C51.3027 4.87995 52.3879 5.96515 53.7267 5.96515Z" fill="currentColor" />
                <path d="M4.07269 5.59605C4.63953 5.59605 5.09923 5.13648 5.09923 4.56951C5.09923 4.00254 4.63966 3.54297 4.07269 3.54297C3.50571 3.54297 3.04614 4.00254 3.04614 4.56951C3.04614 5.13636 3.50571 5.59605 4.07269 5.59605Z" fill="currentColor" />
                <path d="M7.46853 16.037L5.41138 15.1102C5.32328 15.0705 5.25345 14.9919 5.21829 14.8928L4.39487 12.5774C4.27858 12.2502 3.86713 12.2502 3.75084 12.5774L2.92742 14.8928C2.89213 14.9919 2.82243 15.0705 2.73432 15.1102L0.677175 16.037C0.38658 16.1679 0.38658 16.631 0.677175 16.7619L2.73432 17.6888C2.82243 17.7284 2.89225 17.807 2.92742 17.9061L3.75084 20.2215C3.86713 20.5487 4.27858 20.5487 4.39487 20.2215L5.21829 17.9061C5.25358 17.807 5.32328 17.7284 5.41138 17.6888L7.46853 16.7619C7.75913 16.6309 7.75913 16.1679 7.46853 16.037Z" fill="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_5563_195">
                    <rect width="65" height="65" fill="white" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default TeddyBear