import React from 'react'

const Scissors = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="120" height="120" viewBox="0 0 120 120" fill="none">
            <g clipPath="url(#clip0_5594_874)">
                <path d="M42.4775 85.2499C39.852 83.9761 36.8874 83.801 34.1297 84.7566C31.3723 85.7123 29.1521 87.6848 27.878 90.3105C26.6039 92.9362 26.4289 95.9008 27.3847 98.6583C28.3404 101.416 30.3129 103.636 32.9386 104.91C34.4705 105.653 36.0919 106.005 37.6904 106.005C41.7471 106.005 45.6514 103.737 47.5379 99.8494C48.8117 97.2237 48.987 94.259 48.0312 91.5016C47.0754 88.744 45.1029 86.5237 42.4775 85.2499ZM43.0822 97.687C41.6443 100.65 38.0639 101.891 35.1005 100.454C33.665 99.7572 32.5866 98.5437 32.0642 97.0362C31.5418 95.5287 31.6374 93.908 32.3339 92.4724C33.0305 91.0369 34.2441 89.9585 35.7516 89.4361C36.3926 89.2139 37.0538 89.1035 37.7126 89.1035C38.6035 89.1035 39.4901 89.3055 40.3154 89.7058C41.7509 90.4022 42.829 91.616 43.3517 93.1235C43.8743 94.631 43.7785 96.2517 43.0822 97.687Z" fill="currentColor" />
                <path d="M93.4589 71.8225C88.0244 69.9255 82.229 69.9787 76.8961 71.9359L76.7927 71.7273L95.6221 33.7764C100.031 24.6892 96.225 13.7092 87.138 9.30039L86.7651 9.11946C82.6132 7.10477 77.5968 8.84055 75.5763 12.9876L61.5963 41.0526L47.6514 12.9034C45.6398 8.75219 40.6277 7.00586 36.4708 9.01164L36.0979 9.19164C27.0011 13.5808 23.1712 24.5521 27.5697 33.6686L46.3359 71.7004C46.2977 71.7613 46.2346 71.8907 46.2021 71.9566C34.4744 67.6389 21.1134 72.8514 15.5451 84.3276C9.61589 96.5484 14.7344 111.314 26.9547 117.243C30.3581 118.895 34.0134 119.727 37.6879 119.727C40.405 119.727 43.1327 119.272 45.7757 118.356C51.9925 116.201 56.9983 111.754 59.861 105.853L61.529 102.491L63.1539 105.784C66.0133 111.711 71.0093 116.168 77.2214 118.337C79.8796 119.265 82.6242 119.726 85.3593 119.726C89.0156 119.726 92.6545 118.902 96.0447 117.266C101.971 114.407 106.429 109.411 108.597 103.199C110.766 96.9864 110.386 90.3016 107.526 84.3754C104.667 78.4492 99.671 73.9912 93.4589 71.8225ZM80.0146 15.1851C80.0184 15.1773 80.0224 15.1696 80.0259 15.1619C80.8504 13.4624 82.9038 12.7506 84.6028 13.5752L84.9757 13.7561C91.6059 16.9729 94.3828 24.9841 91.1756 31.5948L74.0301 66.1516L64.3549 46.6216L80.0146 15.1851ZM38.2504 13.652C38.8211 13.3466 39.4776 13.1158 40.1064 13.1322C41.381 13.1322 42.6072 13.8461 43.1971 15.0688C43.2009 15.0766 62.6608 54.3581 62.6608 54.3581L47.2359 62.332L32.021 31.4969C28.8185 24.8596 31.6129 16.8545 38.2504 13.652ZM55.4146 103.672C50.6777 113.436 38.8804 117.525 29.1166 112.788C19.3532 108.05 15.2639 96.2531 20.0008 86.4897C24.7378 76.7263 36.535 72.6367 46.2986 77.3739C47.2038 77.8131 48.2458 77.6502 48.9712 77.0418L58.7721 96.9044L55.4146 103.672ZM103.921 101.566C102.189 106.53 98.6271 110.521 93.8925 112.805C84.1186 117.52 72.3302 113.406 67.605 103.612L49.4278 66.7741L64.8597 58.7964L73.4929 76.2231C74.0955 77.4395 75.5648 77.9444 76.788 77.3542C81.5224 75.07 86.8636 74.7658 91.8267 76.4985C96.79 78.231 100.781 81.7928 103.066 86.5274C105.35 91.262 105.654 96.6032 103.921 101.566Z" fill="currentColor" />
                <path d="M88.9411 84.7646C86.1858 83.8025 83.2207 83.9717 80.5924 85.2397C75.1664 87.8577 72.8817 94.4019 75.4997 99.8279C77.3815 103.728 81.2918 106.005 85.3573 106.005C86.9475 106.005 88.5621 105.657 90.0879 104.92C95.5137 102.302 97.7984 95.7582 95.1804 90.3322C93.9122 87.7037 91.6964 85.7263 88.9411 84.7646ZM87.9359 100.46C84.9692 101.891 81.3914 100.642 79.9603 97.6759C78.529 94.7094 79.778 91.1314 82.7447 89.7003C83.5669 89.3038 84.4491 89.1038 85.3357 89.1038C85.9988 89.1038 86.6646 89.2156 87.3089 89.4406C88.8153 89.9666 90.0267 91.0475 90.72 92.4845C92.1511 95.4507 90.9021 99.0284 87.9359 100.46Z" fill="currentColor" />
                <path d="M59.0961 66.5947C56.3949 67.8981 55.2577 71.1559 56.5611 73.8571C57.1922 75.1658 58.2954 76.1502 59.667 76.629C60.2541 76.8339 60.86 76.9358 61.4639 76.9358C62.2714 76.9358 63.0748 76.7537 63.8234 76.3925C66.5246 75.0892 67.662 71.8314 66.3586 69.1302C65.0553 66.4286 61.7972 65.2914 59.0961 66.5947ZM63.7896 72.3067C63.5724 72.929 63.1259 73.4296 62.5322 73.716C61.9386 74.0022 61.2689 74.0404 60.6464 73.8231C60.0242 73.6058 59.5238 73.1593 59.2374 72.5657C58.6461 71.3401 59.1622 69.8622 60.3875 69.2708C60.9821 68.984 61.6524 68.9467 62.273 69.1635C62.8953 69.3808 63.3959 69.8272 63.6823 70.4209C63.9687 71.0148 64.0069 71.6844 63.7896 72.3067Z" fill="currentColor" />
                <path d="M48.9293 51.502C49.2788 51.502 49.6339 51.4277 49.9716 51.2704C51.2119 50.6938 51.7498 49.2213 51.1732 47.9809C50.1839 45.8528 49.2352 43.9204 48.2307 41.8745C47.9257 41.2534 47.6173 40.6256 47.3039 39.9838C46.7037 38.7548 45.2206 38.2452 43.992 38.8457C42.7632 39.4459 42.2536 40.9288 42.8539 42.1577C43.1689 42.8024 43.4787 43.4334 43.785 44.0573C44.7776 46.0788 45.7151 47.988 46.6824 50.0685C47.1017 50.9713 47.9956 51.502 48.9293 51.502Z" fill="currentColor" />
                <path d="M43.437 31.996L41.957 28.9374C41.3614 27.7063 39.8804 27.1913 38.6495 27.7867C37.4183 28.3822 36.9031 29.8632 37.4987 31.0942L38.979 34.1532C39.4065 35.0368 40.2898 35.5515 41.2098 35.5515C41.5716 35.5515 41.9391 35.4718 42.2867 35.3035C43.5177 34.7082 44.0328 33.2272 43.437 31.996Z" fill="currentColor" />
                <path d="M17.9046 48.5026L12.6499 46.1352C12.4249 46.0339 12.2466 45.8331 12.1566 45.5799L10.0533 39.6657C9.75609 38.8302 8.70516 38.8302 8.40797 39.6657L6.30469 45.5799C6.21469 45.8331 6.03633 46.0339 5.81133 46.1352L0.556875 48.5026C-0.185625 48.8371 -0.185625 50.0199 0.556875 50.3544L5.81156 52.7218C6.03656 52.8231 6.21492 53.0239 6.30492 53.2771L8.4082 59.1913C8.70539 60.0268 9.75633 60.0268 10.0535 59.1913L12.1568 53.2771C12.2468 53.0239 12.4252 52.8231 12.6502 52.7218L17.9048 50.3544C18.6469 50.0199 18.6469 48.8371 17.9046 48.5026Z" fill="currentColor" />
                <path d="M115.407 36.9903C117.944 36.9903 120 34.934 120 32.3975C120 29.861 117.944 27.8047 115.407 27.8047C112.871 27.8047 110.814 29.861 110.814 32.3975C110.814 34.934 112.871 36.9903 115.407 36.9903Z" fill="currentColor" />
                <path d="M17.493 15.7148C17.5134 14.6405 16.6591 13.7531 15.5849 13.7327C14.5107 13.7123 13.6233 14.5666 13.6029 15.6409C13.5825 16.7151 14.4368 17.6025 15.511 17.6229C16.5852 17.6433 17.4726 16.789 17.493 15.7148Z" fill="currentColor" />
                <path d="M98.2176 8.65924L102.115 10.4154C102.282 10.4906 102.415 10.6395 102.481 10.8272L104.042 15.2142C104.262 15.8339 105.041 15.8339 105.262 15.2142L106.822 10.8272C106.889 10.6395 107.021 10.4906 107.188 10.4154L111.086 8.65924C111.637 8.41103 111.637 7.53377 111.086 7.28557L107.188 5.52963C107.021 5.45439 106.889 5.30557 106.822 5.1176L105.262 0.730566C105.041 0.110645 104.262 0.110645 104.042 0.730566L102.481 5.1176C102.415 5.30557 102.282 5.45439 102.115 5.52963L98.2176 7.28557C97.6671 7.53377 97.6671 8.41103 98.2176 8.65924Z" fill="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_5594_874">
                    <rect width="120" height="120" fill="white" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default Scissors