import React from 'react'

const <PERSON><PERSON><PERSON><PERSON> = ({ width = "40", height = "40" }) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 40 40" fill="none">
            <g clipPath="url(#clip0_5567_61)">
                <path d="M18.0111 30.1745H21.9888C22.2496 30.1745 22.461 29.963 22.461 29.7023V26.9917C22.461 26.7309 22.2496 26.5195 21.9888 26.5195H18.0111C17.7503 26.5195 17.5389 26.731 17.5389 26.9917V29.7023C17.5389 29.963 17.7503 30.1745 18.0111 30.1745ZM18.4833 27.4639H21.5165V29.23H18.4833V27.4639Z" fill="currentColor" />
                <path d="M5.18747 21.0419H6.68606C7.45059 21.0419 8.07262 20.4198 8.07262 19.6553V17.1913C8.07262 16.4267 7.45059 15.8047 6.68606 15.8047H5.18747C4.42293 15.8047 3.8009 16.4266 3.8009 17.1913V19.6553C3.8009 20.4199 4.42293 21.0419 5.18747 21.0419ZM4.74536 17.1913C4.74536 16.9476 4.94372 16.7492 5.18747 16.7492H6.68606C6.92989 16.7492 7.12817 16.9476 7.12817 17.1913V19.6554C7.12817 19.8992 6.92981 20.0975 6.68606 20.0975H5.18747C4.94364 20.0975 4.74536 19.8991 4.74536 19.6554V17.1913Z" fill="currentColor" />
                <path d="M33.4472 21.0419H34.9458C35.7104 21.0419 36.3325 20.4198 36.3325 19.6553V17.1913C36.3325 16.4267 35.7104 15.8047 34.9458 15.8047H33.4472C32.6827 15.8047 32.0607 16.4266 32.0607 17.1913V19.6553C32.0607 20.4199 32.6828 21.0419 33.4472 21.0419ZM33.0052 17.1913C33.0052 16.9476 33.2035 16.7492 33.4473 16.7492H34.9459C35.1897 16.7492 35.3881 16.9476 35.3881 17.1913V19.6554C35.3881 19.8992 35.1897 20.0975 34.9459 20.0975H33.4473C33.2035 20.0975 33.0052 19.8991 33.0052 19.6554V17.1913Z" fill="currentColor" />
                <path d="M5.18747 30.2411H6.68606C7.45059 30.2411 8.07262 29.6191 8.07262 28.8545V26.3905C8.07262 25.6259 7.45059 25.0039 6.68606 25.0039H5.18747C4.42293 25.0039 3.8009 25.6259 3.8009 26.3905V28.8545C3.8009 29.6191 4.42293 30.2411 5.18747 30.2411ZM4.74536 26.3905C4.74536 26.1467 4.94372 25.9484 5.18747 25.9484H6.68606C6.92989 25.9484 7.12817 26.1468 7.12817 26.3905V28.8546C7.12817 29.0984 6.92981 29.2967 6.68606 29.2967H5.18747C4.94364 29.2967 4.74536 29.0984 4.74536 28.8546V26.3905Z" fill="currentColor" />
                <path d="M39.2332 34.9055C39.3673 34.5934 39.4384 34.2548 39.4384 33.9075V13.7485C39.4384 12.3536 38.3034 11.2188 36.9085 11.2188H31.4846C30.2759 11.2188 29.2631 12.071 29.014 13.2059H11.242C10.9726 12.0681 9.94891 11.2188 8.72992 11.2188H3.14359C1.71992 11.2188 0.561641 12.377 0.561641 13.8007V34.5632C0.561641 34.7273 0.577031 34.8891 0.607266 35.048C0.2325 35.4204 0 35.9359 0 36.5047V37.7348C0 38.8677 0.921719 39.7894 2.05461 39.7894H37.9454C39.0783 39.7894 40 38.8677 40 37.7348V36.5047C40 35.8591 39.7004 35.2823 39.2332 34.9055ZM31.4847 12.7928H36.9086C37.4356 12.7928 37.8643 13.2216 37.8643 13.7485V33.9075C37.8643 34.1034 37.8041 34.2914 37.6942 34.4502H37.2158V32.0063C37.2158 31.0981 36.477 30.3594 35.5689 30.3594C34.6609 30.3594 33.9221 31.0981 33.9221 32.0063V34.4502H32.6791V31.6002C34.5134 30.871 35.8142 29.0792 35.8142 26.9882C35.8142 24.2521 33.5883 22.0262 30.8522 22.0262C30.7434 22.0262 30.6359 22.0309 30.5289 22.0379V13.7485C30.529 13.2215 30.9577 12.7928 31.4847 12.7928ZM36.2713 34.4501H34.8666V32.0062C34.8666 31.6188 35.1816 31.3038 35.5689 31.3038C35.9562 31.3038 36.2713 31.6188 36.2713 32.0062V34.4501ZM29.0253 34.307H24.2667V26.4979C24.2667 26.1905 24.1889 25.9009 24.0521 25.6477H26.0756C25.9557 26.0745 25.8902 26.5236 25.8902 26.988C25.8902 29.0791 27.191 30.8708 29.0253 31.6001V34.307ZM16.6777 26.4979C16.6777 26.0291 17.0591 25.6477 17.5279 25.6477H22.4721C22.9409 25.6477 23.3223 26.0291 23.3223 26.4979V34.2134H22.4611V31.4934C22.4611 31.2326 22.2497 31.0212 21.9889 31.0212H18.0112C17.7505 31.0212 17.5391 31.2326 17.5391 31.4934V34.2134H16.6779V26.4979H16.6777ZM18.4834 34.2134V31.9656H21.5166V34.2134H18.4834ZM26.4497 24.7034H22.4721H17.5279H11.3119V22.9705H27.9452C27.3219 23.4227 26.8079 24.016 26.4497 24.7034ZM28.9549 22.026H11.312V20.2932H28.9549V22.026ZM15.9479 25.6477C15.8111 25.9009 15.7333 26.1905 15.7333 26.4979V34.307H12.6109V32.0063C12.6109 31.2176 12.0534 30.5573 11.3119 30.3971V25.6478H15.9479V25.6477ZM29.9698 34.4501V31.8699C30.2564 31.9216 30.5509 31.9501 30.8523 31.9501C31.1535 31.9501 31.448 31.9216 31.7347 31.8699V34.4501H29.9698ZM30.8523 31.0057C28.637 31.0057 26.8347 29.2034 26.8347 26.9881C26.8347 24.7728 28.637 22.9705 30.8523 22.9705C33.0676 22.9705 34.8698 24.7728 34.8698 26.9881C34.8698 29.2034 33.0676 31.0057 30.8523 31.0057ZM28.9549 19.3488H11.312V17.616H28.9549V19.3488ZM10.2617 32.0062C10.2617 31.6188 10.5768 31.3038 10.9641 31.3038C11.3514 31.3038 11.6665 31.6188 11.6665 32.0062V34.4501H10.2617V32.0062ZM28.9549 16.6716H11.312V14.78H28.9549V16.6716ZM3.14359 12.7928H8.72992C9.2857 12.7928 9.73781 13.2449 9.73781 13.8007V30.8786V30.9108C9.47719 31.2022 9.31727 31.5854 9.31727 32.0062V34.4501H8.50227V32.0062C8.50227 31.098 7.76352 30.3593 6.85539 30.3593C5.94734 30.3593 5.20859 31.098 5.20859 32.0062V34.4501H4.3893V32.0062C4.3893 31.098 3.65055 30.3593 2.74242 30.3593C2.52805 30.3593 2.32375 30.4017 2.1357 30.4766V13.8007C2.1357 13.2449 2.58789 12.7928 3.14359 12.7928ZM7.55781 34.4501H6.15297V32.0062C6.15297 31.6188 6.46805 31.3038 6.85539 31.3038C7.24273 31.3038 7.55781 31.6188 7.55781 32.0062V34.4501ZM3.44484 34.4501H2.1357V31.6539C2.2575 31.4449 2.48352 31.3038 2.74242 31.3038C3.12977 31.3038 3.44484 31.6188 3.44484 32.0062V34.4501ZM38.4259 37.7348C38.4259 37.9998 38.2103 38.2153 37.9454 38.2153H2.05461C1.78961 38.2153 1.57406 37.9998 1.57406 37.7348V36.5047C1.57406 36.2397 1.78961 36.0241 2.05461 36.0241H37.9454C38.2103 36.0241 38.4259 36.2397 38.4259 36.5047V37.7348Z" fill="currentColor" />
                <path d="M29.6775 26.2295C30.1004 26.1295 30.3622 25.7056 30.2623 25.2827C30.1623 24.8597 29.7384 24.5979 29.3155 24.6979C28.8925 24.7979 28.6307 25.2218 28.7307 25.6447C28.8307 26.0676 29.2546 26.3294 29.6775 26.2295Z" fill="currentColor" />
                <path d="M33.0111 26.0391C32.7503 26.0391 32.5389 26.2505 32.5389 26.5113V27.4635C32.5389 27.7243 32.7503 27.9357 33.0111 27.9357C33.2719 27.9357 33.4834 27.7243 33.4834 27.4635V26.5113C33.4834 26.2505 33.2719 26.0391 33.0111 26.0391Z" fill="currentColor" />
                <path d="M30.8523 27.7695C30.5915 27.7695 30.3801 27.981 30.3801 28.2417V29.194C30.3801 29.4548 30.5915 29.6662 30.8523 29.6662C31.1131 29.6662 31.3245 29.4548 31.3245 29.194V28.2417C31.3245 27.981 31.1131 27.7695 30.8523 27.7695Z" fill="currentColor" />
                <path d="M4.07014 3.80721L5.74022 4.55963C5.8117 4.59182 5.86842 4.65564 5.89702 4.73611L6.56553 6.61588C6.65999 6.88143 6.99397 6.88143 7.08842 6.61588L7.75694 4.73611C7.78553 4.65564 7.84225 4.59182 7.91374 4.55963L9.58381 3.80721C9.81975 3.70088 9.81975 3.32494 9.58381 3.21869L7.91374 2.46627C7.84225 2.43408 7.78553 2.37025 7.75694 2.28979L7.08842 0.410098C6.99397 0.144551 6.65999 0.144551 6.56553 0.410098L5.89702 2.28979C5.86842 2.37025 5.81178 2.43408 5.74022 2.46627L4.07014 3.21869C3.8342 3.32502 3.8342 3.70096 4.07014 3.80721Z" fill="currentColor" />
                <path d="M34.0283 4.74784C34.7143 4.3238 34.9267 3.4239 34.5027 2.73785C34.0786 2.05181 33.1787 1.83941 32.4927 2.26345C31.8066 2.6875 31.5942 3.5874 32.0183 4.27344C32.4423 4.95949 33.3422 5.17188 34.0283 4.74784Z" fill="currentColor" />
                <path d="M13.8116 8.38094C14.153 8.38094 14.4297 8.10414 14.4297 7.76273C14.4297 7.42133 14.1529 7.14453 13.8116 7.14453C13.4702 7.14453 13.1934 7.42133 13.1934 7.76273C13.1934 8.10414 13.4701 8.38094 13.8116 8.38094Z" fill="currentColor" />
                <path d="M23.1749 7.03482L24.4137 7.59295C24.4668 7.61686 24.5088 7.66412 24.5301 7.72381L25.0259 9.11818C25.096 9.31521 25.3437 9.31521 25.4138 9.11818L25.9097 7.72381C25.9309 7.66412 25.9729 7.61686 26.026 7.59295L27.2648 7.03482C27.4398 6.956 27.4398 6.67709 27.2648 6.59826L26.0259 6.04014C25.9729 6.01623 25.9308 5.96896 25.9096 5.90928L25.4137 4.5149C25.3436 4.31795 25.0959 4.31795 25.0258 4.5149L24.53 5.90928C24.5087 5.96896 24.4667 6.01623 24.4136 6.04014L23.1748 6.59826C22.9998 6.67709 22.9998 6.956 23.1749 7.03482Z" fill="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_5567_61">
                    <rect width="40" height="40" fill="currentColor" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default Kindergarden