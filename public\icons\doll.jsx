import React from 'react'

const Doll = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="60" height="59" viewBox="0 0 60 59" fill="none">
            <g>
                <path d="M50.2338 38.1576V29.6302C50.2338 27.2582 49.8197 24.9813 49.0621 22.8665C49.0802 22.8014 49.0938 22.7343 49.1007 22.6647L50.1925 11.7139C50.4296 9.33473 48.6871 7.20625 46.3081 6.96906L43.8265 6.72168C42.6741 6.60719 41.5458 6.94762 40.6497 7.68121C40.2235 8.03008 39.8751 8.44867 39.6134 8.91508C38.322 4.67453 34.5572 1.59766 30.1255 1.59766C25.6937 1.59766 21.9289 4.67453 20.6375 8.91508C20.3758 8.44867 20.0274 8.03019 19.6012 7.68121C18.7051 6.9475 17.5767 6.6066 16.4244 6.72168L13.9428 6.96906C11.5638 7.20625 9.82119 9.33473 10.0584 11.7139L11.1502 22.6647C11.1571 22.7342 11.1707 22.8013 11.1888 22.8665C10.4312 24.9813 10.0171 27.2581 10.0171 29.6302V38.6379C9.64529 39.6347 9.44092 40.7125 9.44092 41.8374V48.9913C9.44092 54.0536 13.5594 58.1721 18.6217 58.1721H41.6295C46.6918 58.1721 50.8102 54.0536 50.8102 48.9913V41.8374C50.8102 40.6625 50.5859 39.5396 50.182 38.5061C50.2154 38.3957 50.2338 38.2789 50.2338 38.1576ZM12.3976 29.6301C12.3976 19.855 20.3503 11.9023 30.1253 11.9023C39.9004 11.9023 47.8531 19.8549 47.8531 29.6301V35.0959C47.2952 34.5804 46.6743 34.1321 46.0019 33.7662V30.4673C46.0019 21.7129 38.8797 14.5907 30.1253 14.5907C21.371 14.5907 14.2488 21.7129 14.2488 30.4673V33.7663C13.5763 34.1321 12.9556 34.5804 12.3976 35.0959V29.6301ZM26.1431 9.91844C26.8749 8.24992 28.3989 7.12117 30.1255 7.12117C31.852 7.12117 33.3759 8.25004 34.1078 9.91844C32.8202 9.65875 31.4886 9.52164 30.1255 9.52164C28.7623 9.52164 27.4307 9.65863 26.1431 9.91844ZM44.5735 33.1436C43.6485 32.8295 42.659 32.6566 41.6293 32.6566H18.6215C17.5917 32.6566 16.6021 32.8295 15.6772 33.1436V30.4673C15.6772 22.5006 22.1586 16.0191 30.1253 16.0191C38.0921 16.0191 44.5735 22.5005 44.5735 30.4673V33.1436ZM11.8215 48.9775V41.8373C11.8215 38.0877 14.872 35.0372 18.6215 35.0372H41.6293C45.3788 35.0372 48.4293 38.0877 48.4293 41.8373V48.6075C47.2485 47.5936 45.7215 46.996 44.1127 46.996C40.4637 46.996 37.4951 49.9646 37.4951 53.6136C37.4951 54.3626 37.6201 55.0968 37.8619 55.7914H22.7851C23.0269 55.0969 23.1519 54.3626 23.1519 53.6136C23.1519 49.9646 20.1833 46.996 16.5343 46.996C14.7388 46.996 13.0456 47.7408 11.8215 48.9775ZM42.1577 9.5234C42.5619 9.19258 43.0702 9.03906 43.5903 9.09062L46.0719 9.33801C47.1447 9.445 47.9306 10.4048 47.8236 11.4776L47.0892 18.8439C45.5776 16.4751 43.5835 14.4429 41.2469 12.8857L41.4507 10.8422C41.5025 10.3226 41.7535 9.85422 42.1577 9.5234ZM30.1253 3.97832C33.94 3.97832 37.1047 7.0252 37.632 10.9763C37.0216 10.7297 36.3965 10.5123 35.7583 10.3258C35.0378 7.62391 32.763 5.69266 30.1253 5.69266C27.4877 5.69266 25.2128 7.62402 24.4924 10.3258C23.8542 10.5123 23.2291 10.7297 22.6187 10.9763C23.146 7.0252 26.3106 3.97832 30.1253 3.97832ZM12.4271 11.4776C12.3201 10.4048 13.106 9.445 14.1788 9.33801L16.6604 9.09062C17.1802 9.03906 17.6888 9.19246 18.093 9.5234C18.4971 9.85422 18.7482 10.3226 18.8 10.8423L19.0038 12.8859C16.6672 14.443 14.6731 16.4751 13.1615 18.844L12.4271 11.4776ZM12.1012 50.9184C13.0322 49.3979 14.7165 48.4244 16.5343 48.4244C19.3956 48.4244 21.7235 50.7522 21.7235 53.6136C21.7235 54.3733 21.5593 55.1132 21.2451 55.7914H18.6214C15.5412 55.7914 12.9344 53.732 12.1012 50.9184ZM41.6293 55.7914H39.4019C39.0878 55.1132 38.9235 54.3734 38.9235 53.6136C38.9235 50.7522 41.2514 48.4244 44.1127 48.4244C45.7569 48.4244 47.2918 49.2208 48.259 50.4979C47.5714 53.5245 44.861 55.7914 41.6293 55.7914Z" fill="currentColor" />
                <path d="M21.6368 22.8206C21.1263 22.4065 20.3766 22.4846 19.9625 22.9952C18.8824 24.3269 18.0702 25.8646 17.614 27.442C17.4315 28.0735 17.7953 28.7336 18.4268 28.9162C18.5374 28.9481 18.6487 28.9633 18.7581 28.9633C19.2746 28.9633 19.7504 28.6244 19.9011 28.1034C20.2695 26.8292 20.9301 25.5814 21.8115 24.4948C22.2256 23.9844 22.1474 23.2347 21.6368 22.8206Z" fill="currentColor" />
                <path d="M24.2431 19.5115C23.9391 19.6682 23.637 19.839 23.3451 20.0195C22.7859 20.3651 22.6126 21.0986 22.9581 21.6578C23.1831 22.0221 23.5729 22.2226 23.9718 22.2226C24.1853 22.2226 24.4014 22.1652 24.5964 22.0447C24.8362 21.8966 25.0843 21.7562 25.3338 21.6277C25.9182 21.3265 26.1476 20.6086 25.8465 20.0242C25.5454 19.4398 24.8274 19.2102 24.2431 19.5115Z" fill="currentColor" />
                <path d="M19.9925 46.4693C21.108 46.4693 22.0122 45.565 22.0122 44.4495C22.0122 43.334 21.1079 42.4297 19.9925 42.4297C18.877 42.4297 17.9727 43.334 17.9727 44.4495C17.9727 45.565 18.877 46.4693 19.9925 46.4693Z" fill="currentColor" />
                <path d="M30.4562 49.3366C32.6064 49.3366 34.3558 47.4869 34.3558 45.2132C34.3558 42.9395 32.6064 41.0898 30.4562 41.0898C28.3059 41.0898 26.5566 42.9396 26.5566 45.2132C26.5565 47.4869 28.3059 49.3366 30.4562 49.3366ZM30.4562 42.5182C31.8188 42.5182 32.9274 43.7271 32.9274 45.2132C32.9274 46.6991 31.8188 47.9082 30.4562 47.9082C29.0936 47.9082 27.985 46.6993 27.985 45.2132C27.985 43.7273 29.0935 42.5182 30.4562 42.5182Z" fill="currentColor" />
                <path d="M40.9207 46.4693C42.0362 46.4693 42.9406 45.565 42.9406 44.4495C42.9406 43.334 42.0362 42.4297 40.9207 42.4297C39.8052 42.4297 38.9009 43.334 38.9009 44.4495C38.901 45.565 39.8052 46.4693 40.9207 46.4693Z" fill="currentColor" />
                <path d="M39.8207 27.832C39.4263 27.832 39.1064 28.1518 39.1064 28.5463V29.9154C39.1064 30.3098 39.4263 30.6296 39.8207 30.6296C40.2152 30.6296 40.535 30.3098 40.535 29.9154V28.5463C40.535 28.1518 40.2152 27.832 39.8207 27.832Z" fill="currentColor" />
                <path d="M36.1142 23.4805C35.7197 23.4805 35.3999 23.8003 35.3999 24.1947V25.5637C35.3999 25.9582 35.7197 26.278 36.1142 26.278C36.5086 26.278 36.8284 25.9582 36.8284 25.5637V24.1947C36.8284 23.8003 36.5086 23.4805 36.1142 23.4805Z" fill="currentColor" />
                <path d="M8.60657 4.54922L6.08071 3.41121C5.97255 3.36246 5.88689 3.26602 5.84353 3.14426L4.83243 0.301289C4.68958 -0.10043 4.1845 -0.10043 4.04165 0.301289L3.03056 3.14426C2.98731 3.26602 2.90153 3.36246 2.79337 3.41121L0.267627 4.54922C-0.089209 4.71 -0.089209 5.27859 0.267627 5.43937L2.79349 6.57738C2.90165 6.62613 2.98731 6.72258 3.03067 6.84434L4.04177 9.6873C4.18462 10.089 4.68981 10.089 4.83255 9.6873L5.84364 6.84434C5.88689 6.72258 5.97267 6.62625 6.08083 6.57738L8.60669 5.43937C8.96341 5.27859 8.96341 4.71 8.60657 4.54922Z" fill="currentColor" />
                <path d="M55.625 4.99297C55.625 3.77363 54.6365 2.78516 53.4173 2.78516C52.1979 2.78516 51.2095 3.77363 51.2095 4.99297C51.2095 6.2123 52.1979 7.20066 53.4173 7.20066C54.6366 7.20066 55.625 6.2123 55.625 4.99297Z" fill="currentColor" />
                <path d="M17.9725 2.78781C18.4888 2.78781 18.9074 2.36922 18.9074 1.85289C18.9074 1.33656 18.4888 0.917969 17.9725 0.917969C17.4562 0.917969 17.0376 1.33656 17.0376 1.85289C17.0376 2.36922 17.4562 2.78781 17.9725 2.78781Z" fill="currentColor" />
                <path d="M59.8012 18.679L57.9276 17.8349C57.8473 17.7987 57.7838 17.7272 57.7517 17.637L57.0017 15.5281C56.8957 15.2302 56.521 15.2302 56.415 15.5281L55.665 17.637C55.6329 17.7272 55.5694 17.7988 55.4891 17.8349L53.6155 18.679C53.3508 18.7983 53.3508 19.2201 53.6155 19.3393L55.4891 20.1835C55.5694 20.2197 55.6329 20.2912 55.665 20.3814L56.415 22.4903C56.521 22.7882 56.8957 22.7882 57.0017 22.4903L57.7517 20.3814C57.7838 20.2912 57.8473 20.2196 57.9276 20.1835L59.8012 19.3393C60.0659 19.2201 60.0659 18.7983 59.8012 18.679Z" fill="currentColor" />
            </g>
        </svg>
    )
}

export default Doll