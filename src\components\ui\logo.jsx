import React from 'react'
import { cn } from '@/lib/utils'
import Link from 'next/link'
import Image from 'next/image'

const Logo = ({ className }) => {
  return (
    <Link href="/" className="flex items-center gap-1">
      <Image src='/images/logo.webp' width={220} height={40} alt="img" /> 
      {/* <span className={cn("font-bold text-3xl", className)}>Ascent</span> */}
    </Link>
  )
}

export default Logo