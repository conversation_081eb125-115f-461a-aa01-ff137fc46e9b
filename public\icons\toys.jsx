import React from 'react'

const Toys = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
            <g clipPath="url(#clip0_5563_264)">
                <path d="M24.1996 27.5867C24.7866 27.5731 25.2514 27.0863 25.2378 26.4993C25.2242 25.9124 24.7373 25.4475 24.1504 25.4612C23.5634 25.4748 23.0986 25.9617 23.1122 26.5486C23.1258 27.1356 23.6126 27.6004 24.1996 27.5867Z" fill="currentColor" />
                <path d="M35.7781 27.3171C36.365 27.3035 36.8299 26.8167 36.8163 26.2297C36.8027 25.6429 36.3158 25.178 35.7289 25.1917C35.1419 25.2053 34.6771 25.6922 34.6907 26.2791C34.7043 26.866 35.1912 27.3307 35.7781 27.3171Z" fill="currentColor" />
                <path d="M29.9721 26.7383C29.7556 26.7434 29.5529 26.8463 29.4211 27.0182L28.1468 28.6807C27.9065 28.9943 27.9658 29.4433 28.2792 29.6835C28.4091 29.783 28.562 29.8311 28.7139 29.8311C28.9287 29.8311 29.1413 29.7347 29.282 29.5511L30.0153 28.5946L30.7919 29.516C31.0466 29.818 31.4976 29.8563 31.7998 29.6019C32.1018 29.3473 32.1403 28.8961 31.8857 28.594L30.5356 26.9923C30.3961 26.8267 30.1907 26.7358 29.9721 26.7383Z" fill="currentColor" />
                <path d="M27.7779 22.8828C27.8706 22.8828 27.9647 22.872 28.0589 22.8492C28.6502 22.7062 29.2805 22.6756 29.8824 22.7606C30.5349 22.8536 31.1375 22.3988 31.2294 21.7469C31.3216 21.095 30.8677 20.4918 30.2158 20.3998C29.3063 20.2713 28.3922 20.3157 27.4987 20.5318C26.8587 20.6864 26.4653 21.3306 26.6201 21.9706C26.7521 22.5164 27.24 22.8828 27.7779 22.8828Z" fill="currentColor" />
                <path d="M24.7811 24.555C25.096 24.555 25.4103 24.4311 25.6446 24.1851C25.7193 24.1066 25.8032 24.0288 25.9011 23.9468C26.4064 23.5249 26.474 22.773 26.0518 22.2678C25.6297 21.7625 24.8779 21.695 24.3727 22.1171C24.2046 22.2575 24.0559 22.3961 23.918 22.5408C23.4641 23.0175 23.4825 23.7721 23.9592 24.2262C24.19 24.4461 24.4858 24.555 24.7811 24.555Z" fill="currentColor" />
                <path d="M57.229 32.8663H54.4609L54.8703 32.1174C55.763 30.4848 55.1611 28.4304 53.5286 27.5377L46.5011 23.6951C44.8683 22.8025 42.8139 23.4045 41.9212 25.0369L41.192 26.3705C41.1382 25.7637 41.0215 25.1731 40.8479 24.6032C42.4446 23.9112 43.5479 22.3027 43.5052 20.4586C43.4489 18.0305 41.432 16.0971 38.9996 16.1574C37.4002 16.1944 35.9726 17.0987 35.2318 18.4575C33.5981 17.7068 31.7296 17.3004 29.7543 17.3473C27.7993 17.3927 25.9471 17.8852 24.3377 18.7164C23.5527 17.418 22.1284 16.5861 20.5659 16.5861C20.5321 16.5861 20.4979 16.5864 20.4637 16.5872C19.6168 16.6068 18.8139 16.865 18.1335 17.3159L14.5543 7.07477C14.0496 5.63066 12.6827 4.66023 11.1529 4.66023C10.7405 4.65789 3.94277 4.70934 3.57879 4.70992C1.60547 4.72363 0 6.33988 0 8.31344C0 9.28035 0.37793 10.1882 1.06395 10.8698C1.75008 11.5512 2.66484 11.9208 3.62754 11.9166L8.60086 11.8829L15.9373 32.875C14.5063 32.9818 13.3743 34.1795 13.3743 35.6375V37.8052C13.3743 39.1173 14.2914 40.2187 15.5181 40.5037V47.8286C15.5181 50.4026 17.6122 52.4965 20.1862 52.4965H40.4323C41.2755 56.549 44.874 59.6035 49.1729 59.6035C54.096 59.6035 58.1011 55.5984 58.1011 50.6754C58.1011 49.9015 58.0018 49.1504 57.8157 48.4338C57.8416 48.2336 57.8559 48.0311 57.8559 47.8286V40.5038C59.0827 40.2188 59.9999 39.1175 59.9999 37.8052V35.6375C60 34.1095 58.7569 32.8663 57.229 32.8663ZM44.0133 26.1809C44.2751 25.7017 44.8781 25.5249 45.3572 25.7871L52.3847 29.6296C52.8638 29.8916 53.0404 30.4945 52.7784 30.9735L51.7434 32.8663H50.376L50.9475 31.8209C51.321 31.1378 51.069 30.2779 50.3859 29.9042L46.2047 27.618C45.8734 27.4369 45.4917 27.3958 45.13 27.5018C44.768 27.6079 44.4688 27.8486 44.2879 28.1796L42.0016 32.3608C41.9145 32.5202 41.8607 32.6914 41.8391 32.8663H40.3577L44.0133 26.1809ZM43.3556 32.8663L45.5344 28.8818L49.6839 31.1507L48.7457 32.8663H43.3556ZM39.0551 18.541C40.1715 18.511 41.0958 19.4002 41.1217 20.5138C41.1416 21.3777 40.6126 22.1271 39.855 22.433C39.1957 21.3797 38.3223 20.4456 37.2875 19.6743C37.6137 19.0082 38.291 18.5588 39.0551 18.541ZM29.8097 19.731C29.8923 19.7291 29.9746 19.7282 30.0566 19.7282C33.5562 19.7282 36.5934 21.4587 38.0033 23.9771C38.4458 24.9326 38.8588 25.8814 38.8409 26.9934C38.8935 29.2671 37.7509 31.3899 35.695 32.8663H23.5682L21.2075 26.1116C21.23 26.0534 21.2495 25.9934 21.263 25.9304C22.0076 22.4358 25.6021 19.8288 29.8097 19.731ZM20.5192 18.9709C21.2834 18.9527 21.9825 19.372 22.3391 20.0242C21.4036 20.7886 20.6139 21.691 20.014 22.6964L18.9803 19.7386C19.3515 19.2679 19.9106 18.9851 20.5192 18.9709ZM9.4452 9.49281C9.44238 9.49281 9.43969 9.49281 9.43699 9.49281L3.61148 9.5323C3.28336 9.53242 2.97621 9.40879 2.74418 9.17816C2.51203 8.94766 2.38418 8.64051 2.38418 8.31344C2.38418 7.64582 2.92734 7.09902 3.59496 7.09445C3.90527 7.09398 10.8328 7.0423 11.153 7.04477C11.6705 7.04477 12.133 7.37301 12.3036 7.86156L21.0424 32.8664H18.46L10.5704 10.2917C10.4031 9.81297 9.95156 9.49281 9.4452 9.49281ZM15.7586 35.6375C15.7586 35.4242 15.932 35.2506 16.1453 35.2506H36.0559C36.0598 35.2506 36.0635 35.2513 36.0674 35.2513C36.0707 35.2513 36.0739 35.2506 36.0771 35.2506H57.2289C57.4421 35.2506 57.6156 35.4242 57.6156 35.6375V37.8052C57.6156 38.0185 57.4421 38.1919 57.2289 38.1919H16.1453C15.932 38.1919 15.7586 38.0185 15.7586 37.8052V35.6375ZM17.9024 47.8286V40.5762H55.4719V44.3546C53.8561 42.7444 51.6289 41.7475 49.173 41.7475C44.4394 41.7475 40.5564 45.4511 40.2647 50.1123H20.1862C18.927 50.1123 17.9024 49.0879 17.9024 47.8286ZM49.1729 57.2193C45.5647 57.2193 42.6292 54.2838 42.6292 50.6755C42.6292 47.0673 45.5647 44.1318 49.1729 44.1318C52.7812 44.1318 55.7168 47.0673 55.7168 50.6755C55.7169 54.2836 52.7814 57.2193 49.1729 57.2193Z" fill="currentColor" />
                <path d="M49.173 47.6797C47.5219 47.6797 46.1787 49.0229 46.1787 50.6739C46.1787 52.325 47.5219 53.6682 49.173 53.6682C50.824 53.6682 52.1672 52.325 52.1672 50.6739C52.1672 49.0229 50.824 47.6797 49.173 47.6797ZM49.173 52.2377C48.3108 52.2377 47.6092 51.5362 47.6092 50.6739C47.6092 49.8118 48.3107 49.1102 49.173 49.1102C50.0352 49.1102 50.7367 49.8118 50.7367 50.6739C50.7367 51.5362 50.0352 52.2377 49.173 52.2377Z" fill="currentColor" />
                <path d="M38.1091 42.0664C37.7141 42.0664 37.3938 42.3867 37.3938 42.7817V44.2243C37.3938 44.6193 37.7141 44.9396 38.1091 44.9396C38.5042 44.9396 38.8244 44.6193 38.8244 44.2243V42.7817C38.8243 42.3867 38.504 42.0664 38.1091 42.0664Z" fill="currentColor" />
                <path d="M33.1186 45.1953C32.7236 45.1953 32.4033 45.5156 32.4033 45.9106V47.3531C32.4033 47.7481 32.7236 48.0684 33.1186 48.0684C33.5137 48.0684 33.8339 47.7481 33.8339 47.3531V45.9106C33.8339 45.5156 33.5137 45.1953 33.1186 45.1953Z" fill="currentColor" />
                <path d="M30.5867 42.0664C30.1916 42.0664 29.8713 42.3867 29.8713 42.7817V44.2243C29.8713 44.6193 30.1916 44.9396 30.5867 44.9396C30.9817 44.9396 31.302 44.6193 31.302 44.2243V42.7817C31.302 42.3867 30.9817 42.0664 30.5867 42.0664Z" fill="currentColor" />
                <path d="M50.2067 17.0332L52.7363 18.1729C52.8446 18.2217 52.9303 18.3184 52.9738 18.4402L53.9864 21.2874C54.1295 21.6897 54.6354 21.6897 54.7784 21.2874L55.791 18.4402C55.8344 18.3184 55.9201 18.2217 56.0284 18.1729L58.558 17.0332C58.9155 16.8721 58.9155 16.3027 58.558 16.1417L56.0284 15.0021C55.9201 14.9532 55.8344 14.8566 55.791 14.7348L54.7784 11.8876C54.6353 11.4854 54.1294 11.4854 53.9864 11.8876L52.9738 14.7348C52.9305 14.8566 52.8447 14.9532 52.7363 15.0021L50.2067 16.1417C49.8492 16.3029 49.8492 16.8723 50.2067 17.0332Z" fill="currentColor" />
                <path d="M46.4745 10.2423C47.6956 10.2423 48.6855 9.25238 48.6855 8.03129C48.6855 6.8102 47.6956 5.82031 46.4745 5.82031C45.2534 5.82031 44.2635 6.8102 44.2635 8.03129C44.2635 9.25238 45.2534 10.2423 46.4745 10.2423Z" fill="currentColor" />
                <path d="M21.7618 9.90391C22.2789 9.90391 22.6981 9.48473 22.6981 8.96758C22.6981 8.45043 22.2789 8.03125 21.7618 8.03125C21.2446 8.03125 20.8254 8.45043 20.8254 8.96758C20.8254 9.48461 21.2447 9.90391 21.7618 9.90391Z" fill="currentColor" />
                <path d="M25.8165 4.43533L27.6929 5.28072C27.7733 5.31693 27.8369 5.38854 27.869 5.479L28.6202 7.59107C28.7263 7.88943 29.1016 7.88943 29.2077 7.59107L29.9587 5.479C29.9909 5.38865 30.0546 5.31693 30.1348 5.28072L32.0113 4.43533C32.2763 4.31592 32.2763 3.89346 32.0113 3.77404L30.1348 2.92865C30.0545 2.89244 29.9908 2.82084 29.9587 2.73037L29.2077 0.618301C29.1016 0.319941 28.7263 0.319941 28.6202 0.618301L27.869 2.73037C27.8369 2.82072 27.7732 2.89244 27.6929 2.92865L25.8165 3.77404C25.5514 3.89346 25.5514 4.31592 25.8165 4.43533Z" fill="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_5563_264">
                    <rect width="60" height="60" fill="currentColor" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default Toys