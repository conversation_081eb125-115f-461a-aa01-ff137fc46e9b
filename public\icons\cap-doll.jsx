import React from 'react'

const CapDoll = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="56" height="60" viewBox="0 0 56 60" fill="none">
            <g>
                <path d="M16.7676 35.9531C15.7367 35.9531 14.9009 36.7889 14.9009 37.8197C14.9009 38.8506 15.7367 39.6864 16.7676 39.6864C17.7985 39.6864 18.6341 38.8506 18.6341 37.8197C18.6341 36.7888 17.7985 35.9531 16.7676 35.9531Z" fill="currentColor" />
                <path d="M36.7191 35.9531C35.6882 35.9531 34.8525 36.7889 34.8525 37.8197C34.8525 38.8506 35.6883 39.6864 36.7191 39.6864C37.75 39.6864 38.5857 38.8506 38.5857 37.8197C38.5857 36.7888 37.75 35.9531 36.7191 35.9531Z" fill="currentColor" />
                <path d="M18.5998 32.6257C18.5152 32.5823 17.7438 32.2031 16.7677 32.2031C15.7916 32.2031 15.0203 32.5825 14.9357 32.6257C14.5895 32.8024 14.4521 33.2262 14.6289 33.5722C14.8055 33.9184 15.2294 34.0559 15.5754 33.879C15.5807 33.8764 16.12 33.6103 16.7677 33.6103C17.4122 33.6103 17.9578 33.878 17.96 33.879C18.0625 33.9314 18.1717 33.9561 18.2794 33.9561C18.5353 33.9561 18.7822 33.816 18.9066 33.5722C19.0833 33.2261 18.9459 32.8024 18.5998 32.6257Z" fill="currentColor" />
                <path d="M38.5509 32.6257C38.4663 32.5823 37.695 32.2031 36.7189 32.2031C35.7428 32.2031 34.9714 32.5825 34.8868 32.6257C34.5407 32.8024 34.4033 33.2262 34.58 33.5722C34.7566 33.9184 35.1806 34.0559 35.5266 33.879C35.5318 33.8764 36.0711 33.6103 36.7189 33.6103C37.3635 33.6103 37.909 33.878 37.9112 33.879C38.0136 33.9314 38.1228 33.9561 38.2305 33.9561C38.4865 33.9561 38.7334 33.816 38.8577 33.5722C39.0345 33.2261 38.8972 32.8024 38.5509 32.6257Z" fill="currentColor" />
                <path d="M28.6096 37.1172C28.221 37.1172 27.906 37.4322 27.906 37.8208C27.906 38.462 27.3843 38.9838 26.743 38.9838C26.1019 38.9838 25.58 38.462 25.58 37.8208C25.58 37.4322 25.265 37.1172 24.8764 37.1172C24.4879 37.1172 24.1729 37.4322 24.1729 37.8208C24.1729 39.238 25.3259 40.3909 26.743 40.3909C28.1603 40.3909 29.3132 39.2379 29.3132 37.8208C29.3133 37.4322 28.9982 37.1172 28.6096 37.1172Z" fill="currentColor" />
                <path d="M52.0646 25.545C52.0646 23.1992 50.4618 21.1945 48.2425 20.6383C47.1952 18.6078 45.151 15.2499 41.9078 12.3425C37.592 8.47355 32.4899 6.51172 26.7432 6.51172C20.9964 6.51172 15.8943 8.47344 11.5785 12.3425C8.33535 15.2498 6.29125 18.6077 5.24383 20.6383C3.02465 21.1946 1.42188 23.1992 1.42188 25.545C1.42188 27.518 2.55789 29.2299 4.20953 30.0639C4.30715 30.7857 4.4691 31.485 4.69668 32.1593C3.08805 33.1598 2.08012 34.9374 2.08012 36.8609C2.08012 38.8122 3.09637 40.5295 4.62648 41.5149V44.5408C4.62648 46.918 6.56043 48.8519 8.93746 48.8519H13.9175C12.929 50.827 12.4033 53.019 12.4033 55.2675V58.8271C12.4033 59.4747 12.9284 59.9998 13.576 59.9998C14.2236 59.9998 14.7487 59.4747 14.7487 58.8271V55.2675C14.7487 52.5731 15.6523 49.9769 17.3032 47.8719C17.5306 47.9485 17.7599 48.0208 17.9909 48.0886C17.9881 48.6117 17.9846 49.3354 17.9822 50.0818C17.9809 50.4891 18.0614 50.894 18.2114 51.2715C18.1953 51.2966 18.18 51.3225 18.1669 51.35C17.5586 52.6284 17.2502 53.9994 17.2502 55.4248V59.2303C17.2502 59.6189 17.5652 59.9339 17.9537 59.9339C18.3423 59.9339 18.6573 59.6187 18.6573 59.2303V55.4247C18.6573 54.4504 18.8291 53.5073 19.1631 52.6106C19.9476 53.3431 20.9511 53.7518 22.0144 53.7516C22.0204 53.7516 22.0266 53.7516 22.0325 53.7515C23.3724 53.7458 24.6146 53.0941 25.4407 51.9637L26.5871 50.395L27.712 51.9549C28.5299 53.0893 29.7665 53.7494 31.1043 53.7661C31.1216 53.7663 31.1388 53.7664 31.1561 53.7664C32.3256 53.7663 33.4251 53.2712 34.2391 52.3918C34.6259 53.3502 34.8288 54.3839 34.8288 55.4248V59.2303C34.8288 59.6189 35.1438 59.9339 35.5323 59.9339C35.9209 59.9339 36.2359 59.6187 36.2359 59.2303V55.4247C36.2359 53.8677 35.8506 52.3231 35.1214 50.958C35.1098 50.9364 35.0973 50.9157 35.084 50.8958C35.1509 50.6354 35.1863 50.3667 35.1863 50.0967V48.1831C35.5241 48.0911 35.8587 47.9888 36.1901 47.877C37.8364 49.9802 38.7375 52.5825 38.7375 55.2672V58.8269C38.7375 59.4745 39.2627 59.9996 39.9102 59.9996C40.5578 59.9996 41.0829 59.4745 41.0829 58.8269V55.2672C41.0829 53.024 40.5566 50.8289 39.5679 48.8517H44.5486C46.9258 48.8517 48.8597 46.9177 48.8597 44.5406V41.5147C50.3898 40.5293 51.4061 38.812 51.4061 36.8607C51.4061 34.8547 50.3046 33.013 48.5935 32.0425C48.7891 31.4409 48.9347 30.8203 49.0279 30.1811C50.8136 29.399 52.0646 27.6158 52.0646 25.545ZM9.57461 18.0577H43.9117C44.788 19.2664 45.4724 20.417 45.983 21.3778H7.50332C8.01391 20.4168 8.69828 19.2663 9.57461 18.0577ZM26.7432 8.85711C34.4473 8.85711 39.6092 12.7925 42.8186 16.6504H10.6677C13.8771 12.7925 19.039 8.85711 26.7432 8.85711ZM6.16551 22.8494C6.25785 22.8387 6.34715 22.8162 6.43258 22.7851H47.0537C47.1392 22.8162 47.2284 22.8387 47.3208 22.8494C48.688 23.0075 49.7192 24.1664 49.7192 25.545C49.7192 26.7702 48.9031 27.8079 47.7862 28.1444C47.5258 28.2037 47.279 28.2691 47.0045 28.2596H6.48191C4.98519 28.2596 3.76727 27.0418 3.76727 25.545C3.76738 24.1664 4.7984 23.0075 6.16551 22.8494ZM30.4589 46.4643H23.0274C17.9853 46.4643 13.399 44.0428 10.532 40.1791C10.4941 40.1155 10.4506 40.0568 10.4029 40.0028C9.46445 38.7001 8.71961 37.2386 8.21477 35.6545C9.475 35.1331 13.365 33.395 16.5515 30.6049H36.7018C40.0892 33.5713 44.2721 35.3482 45.245 35.7385C43.1766 42.0855 37.1871 46.4643 30.4589 46.4643ZM7.80871 33.2751C7.28734 32.4459 6.91445 31.553 6.69156 30.6049H12.6895C10.7582 31.8995 8.85836 32.8119 7.80871 33.2751ZM45.4449 33.2751C44.395 32.8116 42.4953 31.8993 40.5641 30.6049H46.5624C46.3394 31.553 45.9663 32.4459 45.4449 33.2751ZM5.69547 34.3142C5.73777 34.3848 5.78113 34.4548 5.8252 34.5246C5.67508 34.7823 5.62223 35.0969 5.70285 35.4085C6.1327 37.0706 6.78883 38.6265 7.63188 40.0481C7.62566 40.0481 7.61945 40.0486 7.61324 40.0486C5.85543 40.0486 4.42539 38.6185 4.42539 36.8608C4.42551 35.8505 4.90797 34.9096 5.69547 34.3142ZM6.97187 44.5408V42.3556C7.18246 42.3801 7.39621 42.3939 7.61336 42.3939C8.13027 42.3939 8.63441 42.3233 9.12098 42.1855C10.5418 43.9367 12.2807 45.4026 14.2395 46.5064H8.93758C7.85371 46.5065 6.97187 45.6247 6.97187 44.5408ZM24.3047 51.1333C23.7452 51.899 22.9149 52.3404 22.0267 52.3443C22.0227 52.3443 22.019 52.3443 22.0149 52.3443C21.1323 52.3443 20.3045 51.9117 19.7423 51.1561C19.5136 50.8488 19.3884 50.4688 19.3896 50.086C19.3915 49.4928 19.3941 48.9138 19.3966 48.4387C20.5736 48.6819 21.7886 48.8096 23.0275 48.8096H26.0028L24.3047 51.1333ZM33.7792 50.0967C33.7792 50.4915 33.6477 50.8818 33.4089 51.1961C32.8391 51.9463 32.0046 52.3722 31.1219 52.3589C30.2346 52.3479 29.4079 51.9005 28.8535 51.1317L27.1792 48.8097H30.4588C31.5857 48.8097 32.6956 48.707 33.7791 48.505L33.7792 50.0967ZM46.5145 44.5408C46.5145 45.6247 45.6326 46.5064 44.5488 46.5064H39.2513C39.98 46.0932 40.6844 45.6263 41.36 45.1066C42.4713 44.2517 43.4754 43.2669 44.3539 42.1825C44.8441 42.3226 45.352 42.3939 45.873 42.3939C46.09 42.3939 46.3038 42.38 46.5145 42.3556V44.5408ZM49.0608 36.8609C49.0608 38.6186 47.6308 40.0487 45.873 40.0487C45.8631 40.0487 45.8534 40.0479 45.8436 40.0479C46.6428 38.7058 47.2677 37.2615 47.6897 35.7563C47.8096 35.3289 47.6763 34.8905 47.3798 34.6006C47.4648 34.468 47.5469 34.334 47.6259 34.1988C48.5051 34.7807 49.0608 35.7792 49.0608 36.8609Z" fill="currentColor" />
                <path d="M12.549 26.2399C13.4429 26.2459 14.3213 26.2488 15.1969 26.2488C16.0724 26.2488 16.9453 26.2459 17.8272 26.2399C18.4747 26.2355 18.9962 25.707 18.9919 25.0593C18.9875 24.4144 18.4634 23.8945 17.8193 23.8945C17.8167 23.8945 17.8139 23.8945 17.8112 23.8945C16.058 23.9065 14.3417 23.9065 12.5649 23.8945C12.5622 23.8945 12.5595 23.8945 12.5569 23.8945C11.9128 23.8945 11.3886 24.4145 11.3843 25.0594C11.3799 25.7071 11.9015 26.2356 12.549 26.2399Z" fill="currentColor" />
                <path d="M7.01547 26.2399H8.60008C9.24766 26.2399 9.77277 25.7148 9.77277 25.0672C9.77277 24.4196 9.24766 23.8945 8.60008 23.8945H7.01547C6.36789 23.8945 5.84277 24.4196 5.84277 25.0672C5.84277 25.7148 6.36789 26.2399 7.01547 26.2399Z" fill="currentColor" />
                <path d="M2.90771 5.3586L5.39607 6.47974C5.5026 6.52778 5.58709 6.62282 5.62975 6.7427L6.62584 9.54349C6.76658 9.93923 7.26428 9.93923 7.4049 9.54349L8.401 6.7427C8.44365 6.62282 8.52803 6.52778 8.63455 6.47974L11.1229 5.3586C11.4745 5.20017 11.4745 4.64001 11.1229 4.48169L8.63455 3.36056C8.52803 3.31251 8.44354 3.21747 8.401 3.09759L7.4049 0.296807C7.26416 -0.0989356 6.76658 -0.0989356 6.62584 0.296807L5.62975 3.09759C5.58709 3.21747 5.50271 3.31251 5.39607 3.36056L2.90771 4.48169C2.55615 4.64001 2.55615 5.20005 2.90771 5.3586Z" fill="currentColor" />
                <path d="M44.4296 6.68373C45.6152 6.49154 46.4206 5.37461 46.2284 4.18901C46.0362 3.0034 44.9192 2.19809 43.7336 2.39028C42.548 2.58248 41.7427 3.6994 41.9349 4.88501C42.1271 6.07061 43.244 6.87593 44.4296 6.68373Z" fill="currentColor" />
                <path d="M1.00307 12.0411C1.51127 12.0182 1.90469 11.5877 1.88179 11.0795C1.8589 10.5713 1.42837 10.1778 0.920173 10.2007C0.411975 10.2236 0.0185575 10.6542 0.0414492 11.1624C0.064341 11.6706 0.494874 12.064 1.00307 12.0411Z" fill="currentColor" />
                <path d="M55.555 12.3557L53.7092 11.524C53.6301 11.4884 53.5675 11.418 53.5359 11.329L52.797 9.25142C52.6926 8.95786 52.3235 8.95786 52.2191 9.25142L51.4802 11.329C51.4486 11.418 51.386 11.4884 51.307 11.524L49.4612 12.3557C49.2004 12.4731 49.2004 12.8887 49.4612 13.0062L51.307 13.8378C51.386 13.8734 51.4487 13.9438 51.4802 14.0328L52.2191 16.1104C52.3235 16.4038 52.6926 16.4038 52.797 16.1104L53.5359 14.0328C53.5675 13.9438 53.6301 13.8734 53.7092 13.8378L55.555 13.0062C55.8158 12.8887 55.8158 12.4731 55.555 12.3557Z" fill="currentColor" />
            </g>
        </svg>
    )
}

export default CapDoll