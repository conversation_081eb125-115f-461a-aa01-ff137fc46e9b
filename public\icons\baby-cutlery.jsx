import React from 'react'

const BabyCutlery = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="120" height="120" viewBox="0 0 120 120" fill="none">
            <path d="M27.7679 102.242C25.0745 102.242 22.8833 104.433 22.8833 107.127C22.8833 109.82 25.0745 112.011 27.7679 112.011C30.4613 112.011 32.6525 109.82 32.6525 107.127C32.6525 104.433 30.4613 102.242 27.7679 102.242ZM27.7679 109.182C26.6345 109.182 25.7124 108.26 25.7124 107.127C25.7124 105.994 26.6345 105.072 27.7679 105.072C28.9013 105.072 29.8234 105.994 29.8234 107.127C29.8234 108.26 28.9013 109.182 27.7679 109.182Z" fill="currentColor" />
            <path d="M73.4664 102.242C70.773 102.242 68.5815 104.433 68.5815 107.127C68.5815 109.82 70.7727 112.011 73.4664 112.011C76.1598 112.011 78.351 109.82 78.351 107.127C78.351 104.433 76.1598 102.242 73.4664 102.242ZM73.4664 109.182C72.333 109.182 71.4107 108.26 71.4107 107.127C71.4107 105.994 72.333 105.072 73.4664 105.072C74.5998 105.072 75.5219 105.994 75.5219 107.127C75.5219 108.26 74.5996 109.182 73.4664 109.182Z" fill="currentColor" />
            <path d="M73.466 16.8828C63.1329 16.8828 54.7266 25.2894 54.7266 35.6223V47.8334C54.7266 54.3702 60.0445 59.6881 66.5812 59.6881H66.6148V64.6041C63.0445 65.3218 60.3469 68.4814 60.3469 72.2598V106.878C60.3469 114.112 66.232 119.997 73.466 119.997C80.6998 119.997 86.5852 114.112 86.5852 106.878V72.2598C86.5852 68.4814 83.8875 65.3218 80.3175 64.6041V59.6881H80.351C86.8877 59.6881 92.2057 54.3702 92.2057 47.8334V35.6223C92.2057 25.2894 83.7991 16.8828 73.466 16.8828ZM75.6023 63.7149H71.3299V60.5795H75.6023V63.7149ZM81.87 72.2598V106.878C81.87 111.512 78.1001 115.282 73.466 115.282C68.832 115.282 65.062 111.512 65.062 106.878V72.2598C65.062 70.5538 66.45 69.1658 68.156 69.1658H78.7758C80.482 69.1658 81.87 70.5538 81.87 72.2598ZM87.4905 47.8334C87.4905 51.7702 84.2878 54.973 80.351 54.973H66.5812C62.6445 54.973 59.4417 51.7702 59.4417 47.8334V35.6223C59.4417 27.8893 65.733 21.598 73.4662 21.598C81.1995 21.598 87.4905 27.8893 87.4905 35.6223V47.8334Z" fill="currentColor" />
            <path d="M73.4665 25.1797C67.6341 25.1797 62.8892 29.9246 62.8892 35.757V45.8668C62.8892 48.916 65.37 51.3971 68.4195 51.3971H78.5138C81.563 51.3971 84.0441 48.9163 84.0441 45.8668V35.757C84.0439 29.9246 79.2987 25.1797 73.4665 25.1797ZM81.2147 45.8668C81.2147 47.3563 80.003 48.568 78.5135 48.568H68.4192C66.9298 48.568 65.7181 47.3563 65.7181 45.8668V35.757C65.7181 31.4846 69.1938 28.0088 73.4663 28.0088C77.7387 28.0088 81.2145 31.4846 81.2145 35.757L81.2147 45.8668Z" fill="currentColor" />
            <path d="M40.5658 16.883C37.3157 16.883 34.6718 19.527 34.6718 22.7771V37.5751C34.6718 37.8988 34.4083 38.1622 34.0846 38.1622C33.761 38.1622 33.4975 37.899 33.4975 37.5751V22.6834C33.4975 19.4848 30.8955 16.883 27.6972 16.883C24.4989 16.883 21.8969 19.4851 21.8969 22.6834V37.5748C21.8969 37.8985 21.6335 38.162 21.3098 38.162C20.9861 38.162 20.7227 37.8987 20.7227 37.5748V22.7769C20.7227 19.5268 18.0787 16.8828 14.8286 16.8828C11.6304 16.8828 9.02832 19.4848 9.02832 22.6831V47.8334C9.02832 54.3702 14.3463 59.6881 20.883 59.6881H20.9165V64.6041C17.3465 65.3218 14.6489 68.4814 14.6489 72.2598V106.878C14.6489 114.112 20.534 119.997 27.768 119.997C35.002 119.997 40.8871 114.112 40.8871 106.878V72.26C40.8871 68.4816 38.1897 65.322 34.6195 64.6044V59.6884H34.653C41.1897 59.6884 46.5079 54.3704 46.5079 47.8337V22.8247C46.5077 19.5484 43.8421 16.883 40.5658 16.883ZM29.9043 63.9341H25.6319V60.3605H29.9043V63.9341ZM36.172 72.26V106.878C36.172 111.512 32.4021 115.282 27.768 115.282C23.1342 115.282 19.3643 111.512 19.3643 106.878V72.26C19.3643 70.554 20.7522 69.166 22.4582 69.166H33.078C34.784 69.166 36.172 70.554 36.172 72.26ZM41.7925 47.8337C41.7925 51.7705 38.5898 54.9732 34.653 54.9732H20.883C16.9462 54.9732 13.7435 51.7705 13.7435 47.8337V22.6834C13.7435 22.085 14.2303 21.5982 14.8286 21.5982C15.4786 21.5982 16.0073 22.127 16.0073 22.7769V37.5748C16.0073 40.4984 18.3857 42.8771 21.3096 42.8771C24.2334 42.8771 26.6118 40.4987 26.6118 37.5748V22.6834C26.6118 22.085 27.0986 21.5982 27.697 21.5982C28.2954 21.5982 28.7821 22.085 28.7821 22.6834V37.5748C28.7821 40.4984 31.1608 42.8771 34.0844 42.8771C37.0082 42.8771 39.3867 40.4987 39.3867 37.5748V22.7769C39.3867 22.127 39.9154 21.5982 40.5656 21.5982C41.2418 21.5982 41.7921 22.1483 41.7921 22.8247V47.8337H41.7925Z" fill="currentColor" />
            <path d="M26.6556 84.5308V76.9982C26.6556 75.696 25.6002 74.6406 24.298 74.6406C22.9961 74.6406 21.9404 75.696 21.9404 76.9982V84.5308C21.9404 85.833 22.9961 86.8884 24.298 86.8884C25.6002 86.8886 26.6556 85.833 26.6556 84.5308Z" fill="currentColor" />
            <path d="M73.4662 95.695C74.2474 95.695 74.8807 95.0617 74.8807 94.2805V91.641C74.8807 90.8598 74.2474 90.2266 73.4662 90.2266C72.685 90.2266 72.0518 90.8598 72.0518 91.641V94.2805C72.0515 95.0615 72.685 95.695 73.4662 95.695Z" fill="currentColor" />
            <path d="M76.9364 80.7422C76.1553 80.7422 75.522 81.3755 75.522 82.1566V84.7962C75.522 85.5773 76.1553 86.2106 76.9364 86.2106C77.7176 86.2106 78.3509 85.5773 78.3509 84.7962V82.1566C78.3509 81.3755 77.7176 80.7422 76.9364 80.7422Z" fill="currentColor" />
            <path d="M103.835 13.0267L108.837 10.7727C109.544 10.4544 109.544 9.32824 108.837 9.00973L103.835 6.75574C103.62 6.65918 103.451 6.46816 103.365 6.22723L101.362 0.596602C101.08 -0.198867 100.079 -0.198867 99.7961 0.596602L97.7936 6.22746C97.7078 6.4684 97.5381 6.65941 97.3239 6.75598L92.3212 9.00996C91.6143 9.32848 91.6143 10.4544 92.3212 10.7729L97.3239 13.0269C97.5381 13.1235 97.7078 13.3145 97.7936 13.5554L99.7961 19.1861C100.079 19.9815 101.08 19.9815 101.362 19.1861L103.365 13.5554C103.451 13.3143 103.62 13.1232 103.835 13.0267Z" fill="currentColor" />
            <path d="M110.892 38.5054C111.278 36.1219 109.659 33.8765 107.276 33.4902C104.892 33.1038 102.647 34.7227 102.261 37.1062C101.874 39.4897 103.493 41.7351 105.877 42.1215C108.26 42.5079 110.506 40.8889 110.892 38.5054Z" fill="currentColor" />
            <path d="M56.6516 18.6307L58.1371 14.4539C58.2006 14.2751 58.3265 14.1333 58.4854 14.0618L62.1962 12.3898C62.7205 12.1536 62.7205 11.3182 62.1962 11.082L58.4854 9.40996C58.3265 9.33848 58.2006 9.19668 58.1371 9.01785L56.6516 4.84105C56.4416 4.2509 55.6996 4.2509 55.4898 4.84105L54.0044 9.01785C53.9408 9.19668 53.8147 9.33848 53.6558 9.40996L49.945 11.082C49.4207 11.3182 49.4207 12.1536 49.945 12.3898L53.6558 14.0618C53.8147 14.1333 53.9406 14.2751 54.0044 14.4539L55.4898 18.6307C55.6996 19.2209 56.4419 19.2209 56.6516 18.6307Z" fill="currentColor" />
        </svg>
    )
}

export default BabyCutlery