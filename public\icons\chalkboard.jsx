import React from 'react'

const Chalkboard = () => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
            <g clipPath="url(#clip0_5567_249)">
                <path d="M39.0823 35.6784V12.4514C39.0823 10.2575 37.2975 8.47266 35.1036 8.47266H5.05488C2.86098 8.47266 1.07613 10.2575 1.07613 12.4514V35.6785C0.568398 35.9095 0.213867 36.4205 0.213867 37.0136V38.532C0.213867 39.341 0.87207 39.9991 1.68105 39.9991H38.4774C39.2864 39.9991 39.9446 39.341 39.9446 38.532V37.0136C39.9446 36.4204 39.59 35.9095 39.0823 35.6784ZM5.05488 10.0539H35.1036C36.4256 10.0539 37.5011 11.1294 37.5011 12.4514V35.5464H36.0244V13.2755C36.0244 12.2741 35.2097 11.4595 34.2084 11.4595H5.95004C4.94871 11.4595 4.13402 12.2742 4.13402 13.2755V35.5464H2.65738V12.4514C2.65738 11.1294 3.73293 10.0539 5.05488 10.0539ZM14.8389 35.5464H8.2966V34.1628C8.2966 33.4688 8.86129 32.9041 9.55535 32.9041H13.5802C14.2743 32.9041 14.8389 33.4688 14.8389 34.1628V35.5464H14.8389ZM13.5801 31.9553H9.55527C8.33801 31.9553 7.34777 32.9456 7.34777 34.1628V35.5464H5.08285V13.2755C5.08285 12.7973 5.47191 12.4083 5.95004 12.4083H34.2084C34.6866 12.4083 35.0756 12.7973 35.0756 13.2755V35.5464H15.7876V34.1628C15.7876 32.9456 14.7973 31.9553 13.5801 31.9553ZM38.3633 38.4178H1.79512V37.1277H38.3633V38.4178Z" fill="currentColor" />
                <path d="M22.0015 28.9922C22.1838 28.9899 22.3505 28.9255 22.483 28.8201C22.5692 29.0097 22.693 29.1823 22.8505 29.328L26.2587 32.4815C26.5637 32.7637 26.9506 32.9031 27.3367 32.9031C27.7642 32.9031 28.1906 32.732 28.5031 32.3943C28.7912 32.0829 28.9408 31.678 28.9244 31.2541C28.9079 30.8302 28.7273 30.438 28.4159 30.1498L25.0078 26.9964C24.6964 26.7083 24.2926 26.5576 23.8675 26.5752C23.4436 26.5916 23.0515 26.7722 22.7633 27.0836C22.6312 27.2264 22.5298 27.3895 22.4586 27.5644C22.3251 27.4664 22.1603 27.4087 21.9816 27.4111C20.5477 27.4291 18.6551 27.4425 17.4151 27.4362C18.0538 26.598 19.1369 25.1605 20.8386 22.7291C21.548 21.7156 22.0083 20.767 22.2071 19.9099C22.2201 19.8559 22.2898 19.3314 22.2855 19.278C22.2855 17.1883 20.5854 15.4883 18.4958 15.4883C16.6889 15.4883 15.125 16.7735 14.7772 18.5443C14.693 18.9727 14.9722 19.3884 15.4006 19.4725C15.8289 19.5567 16.2447 19.2775 16.3288 18.8491C16.5313 17.818 17.4426 17.0695 18.4958 17.0695C19.698 17.0695 20.6788 18.0351 20.7037 19.2314L20.658 19.5895C20.4998 20.2402 20.1248 20.9912 19.543 21.8224C17.6801 24.4841 16.5679 25.9405 15.9703 26.7229C15.2599 27.653 15.0689 27.903 15.2141 28.375C15.2955 28.6395 15.4977 28.843 15.7689 28.9334C15.8751 28.9687 20.3459 29.013 22.0015 28.9922ZM23.4598 27.7279C23.5758 27.6025 23.7337 27.5298 23.9043 27.5231C23.9128 27.5228 23.9212 27.5227 23.9298 27.5227C24.0911 27.5227 24.2443 27.5825 24.3634 27.6928L27.7716 30.8462C27.897 30.9622 27.9697 31.12 27.9763 31.2908C27.983 31.4615 27.9227 31.6245 27.8067 31.7498C27.5673 32.0087 27.1619 32.0244 26.903 31.7849L23.4948 28.6316C23.3694 28.5155 23.2967 28.3577 23.2901 28.187C23.2835 28.0163 23.3437 27.8533 23.4598 27.7279Z" fill="currentColor" />
                <path d="M7.82282 17.7383C7.82243 17.7383 7.82204 17.7383 7.82173 17.7383C7.38509 17.7388 7.03157 18.0934 7.0322 18.53C7.03345 19.4633 7.03321 20.3965 7.0315 21.3298C7.03071 21.7664 7.384 22.1211 7.82071 22.1219H7.8222C8.25821 22.1219 8.61196 21.7688 8.61282 21.3327C8.61454 20.3977 8.61478 19.4627 8.61353 18.5278C8.6129 18.0916 8.259 17.7383 7.82282 17.7383Z" fill="currentColor" />
                <path d="M7.82212 16.3803C8.25876 16.3803 8.61274 16.0263 8.61274 15.5897V15.1617C8.61274 14.7251 8.25876 14.3711 7.82212 14.3711C7.38548 14.3711 7.03149 14.7251 7.03149 15.1617V15.5897C7.03149 16.0263 7.38548 16.3803 7.82212 16.3803Z" fill="currentColor" />
                <path d="M31.0898 27.2752V28.2318C31.0898 28.4938 31.3023 28.7062 31.5642 28.7062C31.8262 28.7062 32.0386 28.4938 32.0386 28.2318V27.2752C32.0386 27.0132 31.8262 26.8008 31.5642 26.8008C31.3023 26.8008 31.0898 27.0132 31.0898 27.2752Z" fill="currentColor" />
                <path d="M29.2903 25.7452C29.5522 25.7452 29.7647 25.5328 29.7647 25.2709V24.3142C29.7647 24.0523 29.5522 23.8398 29.2903 23.8398C29.0283 23.8398 28.8159 24.0523 28.8159 24.3142V25.2709C28.8159 25.5328 29.0283 25.7452 29.2903 25.7452Z" fill="currentColor" />
                <path d="M31.1546 20.8859C31.4165 20.8859 31.6289 20.6734 31.6289 20.4115V19.4548C31.6289 19.1929 31.4165 18.9805 31.1546 18.9805C30.8926 18.9805 30.6802 19.1929 30.6802 19.4548V20.4115C30.6801 20.6735 30.8925 20.8859 31.1546 20.8859Z" fill="currentColor" />
                <path d="M1.91093 4.36869C1.9828 4.40104 2.03968 4.4651 2.06843 4.54596L2.73999 6.43432C2.83491 6.70111 3.17038 6.70111 3.2653 6.43432L3.93687 4.54596C3.96562 4.4651 4.02257 4.40104 4.09437 4.36869L5.7721 3.61275C6.00913 3.50596 6.00913 3.1283 5.7721 3.02158L4.09437 2.26572C4.02257 2.23338 3.96562 2.16932 3.93687 2.08846L3.2653 0.200098C3.17046 -0.0666992 2.83491 -0.0666992 2.73999 0.200098L2.06843 2.08846C2.03968 2.16932 1.98272 2.23338 1.91093 2.26572L0.233193 3.02158C-0.0038379 3.12838 -0.0038379 3.50604 0.233193 3.61275L1.91093 4.36869Z" fill="currentColor" />
                <path d="M29.9289 1.80438C30.1168 1.01637 29.6303 0.225251 28.8423 0.0373604C28.0543 -0.15053 27.2632 0.335959 27.0753 1.12397C26.8874 1.91197 27.3739 2.7031 28.1619 2.89099C28.9499 3.07888 29.7411 2.59239 29.9289 1.80438Z" fill="currentColor" />
                <path d="M14.7245 5.52085C15.0651 5.47901 15.3072 5.169 15.2654 4.82842C15.2236 4.48785 14.9135 4.24567 14.573 4.28752C14.2324 4.32936 13.9902 4.63937 14.0321 4.97995C14.0739 5.32053 14.3839 5.5627 14.7245 5.52085Z" fill="currentColor" />
                <path d="M34.1609 4.73465L35.4054 5.29535C35.4587 5.31934 35.5009 5.36691 35.5222 5.42684L36.0203 6.82762C36.0907 7.02551 36.3396 7.02551 36.41 6.82762L36.9081 5.42684C36.9294 5.36691 36.9717 5.31934 37.025 5.29535L38.2695 4.73465C38.4453 4.65543 38.4453 4.37527 38.2695 4.29606L37.025 3.73535C36.9717 3.71137 36.9294 3.66379 36.9081 3.60387L36.41 2.20316C36.3396 2.0052 36.0907 2.0052 36.0203 2.20316L35.5222 3.60387C35.5009 3.66387 35.4587 3.71137 35.4054 3.73535L34.1609 4.29606C33.9851 4.37527 33.9851 4.65543 34.1609 4.73465Z" fill="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_5567_249">
                    <rect width="40" height="40" fill="currentColor" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default Chalkboard